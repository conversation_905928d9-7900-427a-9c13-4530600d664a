import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, fromEvent } from 'rxjs';
import { map, startWith, distinctUntilChanged } from 'rxjs/operators';
import { BREAKPOINTS, MEDIA_QUERIES } from '../../responsive.config';

export interface BreakpointState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  currentBreakpoint: string;
  screenWidth: number;
  screenHeight: number;
}

@Injectable({
  providedIn: 'root'
})
export class ResponsiveService {
  private breakpointSubject = new BehaviorSubject<BreakpointState>(this.getCurrentBreakpoint());
  public breakpoint$ = this.breakpointSubject.asObservable();

  constructor() {
    if (typeof window !== 'undefined') {
      // Listen for window resize events
      fromEvent(window, 'resize')
        .pipe(
          map(() => this.getCurrentBreakpoint()),
          distinctUntilChanged((prev, curr) => 
            prev.currentBreakpoint === curr.currentBreakpoint
          )
        )
        .subscribe(breakpoint => {
          this.breakpointSubject.next(breakpoint);
        });
    }
  }

  /**
   * Get current breakpoint information
   */
  private getCurrentBreakpoint(): BreakpointState {
    if (typeof window === 'undefined') {
      return {
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        currentBreakpoint: 'lg',
        screenWidth: 1024,
        screenHeight: 768
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;

    let currentBreakpoint = 'xs';
    let isMobile = true;
    let isTablet = false;
    let isDesktop = false;

    if (width >= parseInt(BREAKPOINTS['2xl'])) {
      currentBreakpoint = '2xl';
      isDesktop = true;
      isMobile = false;
    } else if (width >= parseInt(BREAKPOINTS.xl)) {
      currentBreakpoint = 'xl';
      isDesktop = true;
      isMobile = false;
    } else if (width >= parseInt(BREAKPOINTS.lg)) {
      currentBreakpoint = 'lg';
      isDesktop = true;
      isMobile = false;
    } else if (width >= parseInt(BREAKPOINTS.md)) {
      currentBreakpoint = 'md';
      isTablet = true;
      isMobile = false;
    } else if (width >= parseInt(BREAKPOINTS.sm)) {
      currentBreakpoint = 'sm';
      isTablet = true;
      isMobile = false;
    }

    return {
      isMobile,
      isTablet,
      isDesktop,
      currentBreakpoint,
      screenWidth: width,
      screenHeight: height
    };
  }

  /**
   * Check if current screen matches breakpoint
   */
  isBreakpoint(breakpoint: keyof typeof BREAKPOINTS): Observable<boolean> {
    return this.breakpoint$.pipe(
      map(state => {
        const breakpointValue = parseInt(BREAKPOINTS[breakpoint]);
        return state.screenWidth >= breakpointValue;
      })
    );
  }

  /**
   * Get current breakpoint state synchronously
   */
  getCurrentState(): BreakpointState {
    return this.breakpointSubject.value;
  }

  /**
   * Check if device supports touch
   */
  isTouchDevice(): boolean {
    if (typeof window === 'undefined') return false;
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }

  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion(): boolean {
    if (typeof window === 'undefined') return false;
    return window.matchMedia(MEDIA_QUERIES.reducedMotion).matches;
  }

  /**
   * Check if user prefers dark mode
   */
  prefersDarkMode(): boolean {
    if (typeof window === 'undefined') return false;
    return window.matchMedia(MEDIA_QUERIES.darkMode).matches;
  }

  /**
   * Get responsive image sizes
   */
  getImageSizes(baseWidth: number): string {
    const sizes = [
      `(max-width: ${BREAKPOINTS.sm}) ${Math.round(baseWidth * 0.9)}px`,
      `(max-width: ${BREAKPOINTS.md}) ${Math.round(baseWidth * 0.8)}px`,
      `(max-width: ${BREAKPOINTS.lg}) ${Math.round(baseWidth * 0.7)}px`,
      `${baseWidth}px`
    ];
    return sizes.join(', ');
  }

  /**
   * Get responsive classes based on current breakpoint
   */
  getResponsiveClasses(config: {
    mobile: string;
    tablet: string;
    desktop: string;
  }): string {
    const state = this.getCurrentState();
    
    if (state.isMobile) return config.mobile;
    if (state.isTablet) return config.tablet;
    return config.desktop;
  }

  /**
   * Calculate optimal font size based on screen size
   */
  getResponsiveFontSize(baseSizePx: number): string {
    const state = this.getCurrentState();
    let multiplier = 1;

    if (state.isMobile) {
      multiplier = 0.875; // 14px base becomes ~12px
    } else if (state.isTablet) {
      multiplier = 0.9375; // 16px base becomes 15px
    }

    return `${Math.round(baseSizePx * multiplier)}px`;
  }

  /**
   * Get optimal grid columns based on screen size
   */
  getGridColumns(maxColumns: number): number {
    const state = this.getCurrentState();
    
    if (state.isMobile) return 1;
    if (state.isTablet) return Math.min(2, maxColumns);
    return maxColumns;
  }

  /**
   * Check if element is in viewport
   */
  isInViewport(element: HTMLElement): boolean {
    if (typeof window === 'undefined') return false;
    
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= window.innerHeight &&
      rect.right <= window.innerWidth
    );
  }

  /**
   * Get safe area insets for mobile devices
   */
  getSafeAreaInsets(): { top: number; right: number; bottom: number; left: number } {
    if (typeof window === 'undefined' || typeof getComputedStyle === 'undefined') {
      return { top: 0, right: 0, bottom: 0, left: 0 };
    }

    const style = getComputedStyle(document.documentElement);
    
    return {
      top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
      right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
      bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
      left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0')
    };
  }

  /**
   * Optimize images for current device
   */
  getOptimizedImageUrl(baseUrl: string, width?: number): string {
    const state = this.getCurrentState();
    const devicePixelRatio = typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1;
    
    let optimalWidth = width || state.screenWidth;
    
    // Adjust for device pixel ratio
    optimalWidth = Math.round(optimalWidth * devicePixelRatio);
    
    // Add width parameter to URL (assuming your image service supports it)
    const separator = baseUrl.includes('?') ? '&' : '?';
    return `${baseUrl}${separator}w=${optimalWidth}&q=85&f=webp`;
  }

  /**
   * Get performance-optimized loading strategy
   */
  getLoadingStrategy(): 'eager' | 'lazy' {
    const state = this.getCurrentState();
    
    // Use eager loading for above-the-fold content on fast connections
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection && connection.effectiveType === '4g' && !state.isMobile) {
        return 'eager';
      }
    }
    
    return 'lazy';
  }

  /**
   * Monitor performance metrics
   */
  getPerformanceMetrics(): { [key: string]: number } {
    if (typeof performance === 'undefined') return {};
    
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    return {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
      firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
    };
  }
}
