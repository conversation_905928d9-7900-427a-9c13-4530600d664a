/* AI Use Cases Component Styles */

/* Accordion animations */
.accordion-content {
  transition: max-height 0.3s ease-in-out;
}

.max-h-0 {
  max-height: 0;
}

.max-h-screen {
  max-height: 100vh;
}

/* Smooth rotation for chevron */
.rotate-180 {
  transform: rotate(180deg);
}

/* Accordion item spacing */
.accordion-item + .accordion-item {
  margin-top: 1rem;
}

/* Hover effects for accordion headers */
.accordion-item button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Focus styles for accessibility */
.accordion-item button:focus {
  outline: none;
  ring: 2px;
  ring-color: rgb(59, 130, 246);
  ring-offset: 2px;
}

/* Icon container hover effects */
.accordion-item button:hover .w-12 {
  transform: scale(1.05);
}

/* Bullet point styling */
.w-2.h-2.rounded-full {
  background-color: rgb(59, 130, 246);
}

/* Stats section styling */
.grid.grid-cols-2.md\\:grid-cols-4 > div {
  padding: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .accordion-content {
    max-height: none;
  }
  
  .max-h-screen {
    max-height: none;
  }
  
  .accordion-item button {
    padding: 1rem;
  }
  
  .accordion-item button .w-12 {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .accordion-item button .w-12 svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}

/* Custom color utilities */
.text-primary-600 {
  color: rgb(37, 99, 235);
}

.text-secondary-600 {
  color: rgb(219, 39, 119);
}

.text-accent-600 {
  color: rgb(245, 158, 11);
}

.text-warning-600 {
  color: rgb(245, 158, 11);
}
