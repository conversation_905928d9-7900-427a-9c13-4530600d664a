import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CtaSectionComponent } from '../../components/cta-section/cta-section.component';

@Component({
  selector: 'app-resources',
  imports: [CommonModule, CtaSectionComponent],
  templateUrl: './resources.component.html',
  styleUrl: './resources.component.css'
})
export class ResourcesComponent {
  // Resources data
  quickStartSteps = [
    {
      title: 'Installation',
      description: 'Install AI Hub CLI and create your first project',
      code: 'npm install -g @aihub/cli\naihub create my-project',
      color: 'primary'
    },
    {
      title: 'Configuration',
      description: 'Set up your API keys and workspace settings',
      code: 'aihub config set api-key YOUR_KEY\naihub workspace init',
      color: 'secondary'
    },
    {
      title: 'First Agent',
      description: 'Create and deploy your first AI agent',
      link: '#tutorials',
      color: 'accent'
    }
  ];

  apiEndpoints = [
    {
      method: 'POST',
      endpoint: '/v1/chat',
      description: 'Create AI conversations',
      example: '{ "message": "Hello AI", "model": "gpt-4" }',
      color: 'secondary'
    },
    {
      method: 'GET',
      endpoint: '/v1/tasks',
      description: 'Manage your tasks',
      color: 'accent'
    },
    {
      method: 'GET',
      endpoint: '/v1/analytics',
      description: 'Get workspace analytics',
      color: 'warning'
    }
  ];

  tutorials = [
    {
      id: 1,
      title: 'Build Your First AI Agent',
      duration: '15 min read',
      description: 'Learn how to create and deploy your first AI agent using our platform.',
      color: 'primary'
    },
    {
      id: 2,
      title: 'Task Automation',
      duration: '20 min read',
      description: 'Automate workflows with advanced task management features.',
      color: 'secondary'
    },
    {
      id: 3,
      title: 'Team Collaboration',
      duration: '18 min read',
      description: 'Set up collaborative workspaces for your team.',
      color: 'accent'
    }
  ];

  popularResources = [
    {
      title: 'Installation Guide',
      description: 'Complete setup instructions for all platforms',
      icon: 'document',
      color: 'primary'
    },
    {
      title: 'Code Examples',
      description: 'Ready-to-use code snippets and examples',
      icon: 'code',
      color: 'secondary'
    },
    {
      title: 'Integrations',
      description: 'Connect with Slack, Teams, GitHub and more',
      icon: 'link',
      color: 'accent'
    },
    {
      title: 'Support',
      description: 'Get help from our community and support team',
      icon: 'support',
      color: 'warning'
    }
  ];

  // Methods
  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  navigateToFullDocs() {
    // Navigate to full documentation page
    window.location.href = '/resources';
  }
}
