/* About Section Styles */

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}

/* Timeline styles */
.timeline-line {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
}

.timeline-point {
  position: relative;
  z-index: 10;
}

/* Hover effects */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Team member cards */
.team-card {
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.team-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
  border-color: #e5e7eb;
}

/* Value cards animation */
.value-card {
  transition: all 0.3s ease;
}

.value-card:hover {
  transform: translateY(-4px);
}

.value-icon {
  transition: transform 0.3s ease;
}

.value-card:hover .value-icon {
  transform: scale(1.1);
}

/* Testimonial cards */
.testimonial-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.testimonial-card:hover::before {
  opacity: 1;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

/* Statistics animation */
.stat-number {
  animation: countUp 1s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button hover effects */
.btn-hover-effect {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-hover-effect:hover::before {
  left: 100%;
}

/* Social icons */
.social-icon {
  transition: all 0.2s ease;
}

.social-icon:hover {
  transform: scale(1.1);
  background-color: var(--primary-color);
}

/* Mission/Vision cards */
.mission-vision-card {
  position: relative;
  overflow: hidden;
}

.mission-vision-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mission-vision-card:hover::before {
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .timeline-line {
    left: 20px;
  }

  .timeline-point {
    left: 20px;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
  }

  .team-card:hover {
    transform: translateY(-3px);
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Scroll animations */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in-up.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Star ratings */
.star-rating {
  display: flex;
  gap: 2px;
}

.star {
  transition: all 0.2s ease;
}

.star:hover {
  transform: scale(1.1);
}

/* Quote styling */
.quote-mark {
  font-size: 4rem;
  line-height: 1;
  opacity: 0.1;
  position: absolute;
  top: -10px;
  left: 10px;
}
