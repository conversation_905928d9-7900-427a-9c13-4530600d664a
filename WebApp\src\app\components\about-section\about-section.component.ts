import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-about-section',
  imports: [CommonModule],
  templateUrl: './about-section.component.html',
  styleUrl: './about-section.component.css'
})
export class AboutSectionComponent {
  // Core values data
  coreValues = [
    {
      title: 'Innovation',
      description: 'We constantly push boundaries, exploring new technologies and methodologies to deliver cutting-edge solutions.',
      icon: 'innovation',
      color: 'primary'
    },
    {
      title: 'Collaboration',
      description: 'We believe in the power of teamwork, both within our organization and with our users and partners.',
      icon: 'collaboration',
      color: 'secondary'
    },
    {
      title: 'Excellence',
      description: 'We strive for perfection in every detail, ensuring our platform meets the highest standards of quality.',
      icon: 'excellence',
      color: 'accent'
    },
    {
      title: 'Empowerment',
      description: 'We empower individuals and teams to achieve their full potential through intelligent technology solutions.',
      icon: 'empowerment',
      color: 'warning'
    }
  ];

  // Team members data
  teamMembers = [
    {
      name: '<PERSON>',
      role: 'Lead AI Engineer',
      initials: '<PERSON>',
      description: '10+ years in AI/ML development. Previously at Google and Microsoft, specializing in natural language processing and machine learning algorithms.',
      color: 'primary',
      social: {
        linkedin: '#',
        twitter: '#'
      }
    },
    {
      name: '<PERSON>',
      role: 'UX/UI Design Lead',
      initials: 'SP',
      description: '8+ years in product design. Former design lead at Airbnb and Spotify, passionate about creating intuitive and beautiful user experiences.',
      color: 'secondary',
      social: {
        linkedin: '#',
        pinterest: '#'
      }
    },
    {
      name: 'Michael Rodriguez',
      role: 'Full-Stack Developer',
      initials: 'MR',
      description: '12+ years in software development. Expert in Angular, Node.js, and cloud technologies. Previously at Amazon and Tesla.',
      color: 'accent',
      social: {
        linkedin: '#',
        github: '#'
      }
    }
  ];

  // Testimonials data
  testimonials = [
    {
      name: 'Jennifer Davis',
      role: 'Product Manager',
      company: 'TechCorp',
      initials: 'JD',
      rating: 5,
      text: 'AI Hub has completely revolutionized how our team manages projects. The AI-powered insights have helped us identify bottlenecks we never knew existed. Our productivity has increased by 250%!',
      color: 'primary'
    },
    {
      name: 'Robert Chen',
      role: 'CTO',
      company: 'StartupXYZ',
      initials: 'RC',
      rating: 5,
      text: 'The intelligent automation features are incredible. Tasks that used to take hours now complete in minutes. AI Hub has become an essential part of our development workflow.',
      color: 'secondary'
    },
    {
      name: 'Lisa Martinez',
      role: 'Operations Director',
      company: 'GlobalTech',
      initials: 'LM',
      rating: 5,
      text: 'AI Hub\'s collaborative features have transformed how our remote team works together. The real-time insights and smart recommendations have made us more efficient than ever.',
      color: 'accent'
    }
  ];

  // Company statistics
  statistics = [
    { value: '10K+', label: 'Active Users' },
    { value: '50K+', label: 'Projects Managed' },
    { value: '300%', label: 'Avg. Productivity Boost' },
    { value: '99.9%', label: 'Uptime Reliability' }
  ];

  // Timeline data
  timeline = [
    {
      year: '2023',
      title: 'The Vision',
      description: 'IzonTech Solutions identified the growing need for intelligent workspace management. Teams were struggling with fragmented tools and inefficient workflows.',
      subtitle: 'Research & Planning',
      subtitleDescription: 'Extensive market research and user interviews revealed the pain points in modern workspace management.',
      side: 'left'
    },
    {
      year: '2024',
      title: 'Development',
      description: 'Leveraging IzonTech\'s 15+ years of experience, we built AI Hub using the latest AI/ML technologies, cloud infrastructure, and modern web frameworks.',
      subtitle: 'Development Phase',
      subtitleDescription: 'Our expert team began building the core AI engine and user interface with cutting-edge technologies.',
      side: 'right'
    },
    {
      year: '2024',
      title: 'Launch & Growth',
      description: 'AI Hub launched with revolutionary features that immediately transformed how teams collaborate and manage their workflows.',
      subtitle: 'Market Success',
      subtitleDescription: 'Rapid adoption by teams worldwide, with users reporting 300% productivity improvements.',
      side: 'left'
    }
  ];

  // Generate star array for ratings
  getStarArray(rating: number): number[] {
    return Array(rating).fill(0).map((x, i) => i);
  }
}
