<!-- Demo Page -->
<div class="min-h-screen bg-neutral-50">
  <!-- <PERSON> Header with <PERSON> Gradient -->
  <section class="relative overflow-hidden">
    <!-- Gradient Background -->
    <div class="absolute inset-0 bg-gradient-to-r from-purple-500 via-blue-500 to-cyan-500"></div>
    <div class="absolute inset-0 bg-gradient-to-br from-transparent via-blue-500/20 to-purple-600/40"></div>

    <!-- Animated background elements -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 4s;"></div>
    </div>

    <div class="relative container-custom py-24 md:py-32">
      <div class="max-w-4xl mx-auto text-center">
        <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-8 leading-tight">
          Watch AI Hub in Action
        </h1>
        <p class="text-xl md:text-2xl text-white/90 leading-relaxed max-w-3xl mx-auto mb-8">
          Experience the power of AI-driven productivity through interactive demos and live examples
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button (click)="scheduleDemo()" class="btn btn-white btn-lg">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            Schedule Live Demo
          </button>
          <button (click)="requestTrial()" class="btn btn-outline-white btn-lg">
            Start Free Trial
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Interactive Demo Features -->
  <section class="py-20 bg-white">
    <div class="container-custom">
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
          Interactive Demos
        </h2>
        <p class="text-xl text-neutral-600 max-w-3xl mx-auto">
          Try our key features with hands-on interactive demonstrations
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div *ngFor="let feature of demoFeatures" class="card p-6 bg-white border border-neutral-200 hover-lift text-center group cursor-pointer" (click)="playDemo(feature.demoUrl)">
          <div class="w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center transition-all duration-300"
               [ngClass]="{
                 'bg-secondary-500 group-hover:bg-secondary-600': feature.color === 'secondary',
                 'bg-accent-400 group-hover:bg-accent-500': feature.color === 'accent',
                 'bg-primary-500 group-hover:bg-primary-600': feature.color === 'primary',
                 'bg-warning-400 group-hover:bg-warning-500': feature.color === 'warning'
               }">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0 9 9 0 01-18 0z" *ngIf="feature.icon === 'chat'"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" *ngIf="feature.icon === 'automation'"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" *ngIf="feature.icon === 'analytics'"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" *ngIf="feature.icon === 'team'"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-neutral-900 mb-2">{{ feature.title }}</h3>
          <p class="text-neutral-600 text-sm mb-4">{{ feature.description }}</p>
          <div class="inline-flex items-center text-sm font-medium text-primary-500 group-hover:text-primary-600">
            Try Demo
            <svg class="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Video Demos -->
  <section class="py-20 bg-neutral-50">
    <div class="container-custom">
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
          Video Demonstrations
        </h2>
        <p class="text-xl text-neutral-600 max-w-3xl mx-auto">
          Watch detailed walkthroughs of AI Hub features and capabilities
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div *ngFor="let video of videoDemos" class="card bg-white border border-neutral-200 hover-lift overflow-hidden group cursor-pointer" (click)="playVideo(video)">
          <div class="relative">
            <div class="aspect-video bg-gradient-to-br from-neutral-100 to-neutral-200 flex items-center justify-center">
              <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                <svg class="w-6 h-6 text-primary-500 ml-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
            </div>
            <div class="absolute top-4 right-4 bg-black/70 text-white text-xs px-2 py-1 rounded">
              {{ video.duration }}
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold text-neutral-900 mb-2">{{ video.title }}</h3>
            <p class="text-neutral-600 text-sm mb-4">{{ video.description }}</p>
            <div class="inline-flex items-center text-sm font-medium text-primary-500 group-hover:text-primary-600">
              Watch Video
              <svg class="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0 9 9 0 01-18 0z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Dashboard Preview -->
  <app-dashboard-preview></app-dashboard-preview>

  <!-- Call to Action -->
  <app-cta-section></app-cta-section>

  <!-- Video Modal -->
  <div *ngIf="currentVideo" class="fixed inset-0 bg-black/90 video-modal flex items-center justify-center z-50 p-4" (click)="closeVideo()" (keydown.escape)="closeVideo()">
    <div class="relative w-full max-w-5xl aspect-video bg-black rounded-xl overflow-hidden video-container shadow-2xl" (click)="$event.stopPropagation()">
      <!-- Close Button -->
      <button (click)="closeVideo()" class="absolute -top-12 right-0 z-10 w-10 h-10 bg-white/20 hover:bg-white/40 rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-110">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>

      <!-- Loading Spinner -->
      <div class="absolute inset-0 flex items-center justify-center bg-neutral-900">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>

      <!-- Video Iframe -->
      <iframe
        [src]="currentVideo"
        class="w-full h-full relative z-10"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowfullscreen
        loading="lazy">
      </iframe>
    </div>

    <!-- Instructions -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/70 text-sm">
      Press ESC or click outside to close
    </div>
  </div>
</div>
