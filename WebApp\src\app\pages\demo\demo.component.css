/* Demo page specific styles */

/* Custom button styles for demo page */
.btn-white {
  @apply bg-white text-primary-600 border-white hover:bg-neutral-50 hover:text-primary-700;
}

.btn-outline-white {
  @apply bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600;
}

/* Video thumbnail hover effects */
.aspect-video {
  aspect-ratio: 16 / 9;
}

/* Interactive demo card animations */
.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Video modal styles */
.video-modal {
  backdrop-filter: blur(4px);
}

.video-modal .video-container {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

/* Smooth transitions for video cards */
.video-card {
  transition: all 0.3s ease;
}

.video-card:hover {
  transform: translateY(-2px);
}

/* Play button animation */
.play-button {
  transition: all 0.3s ease;
}

.play-button:hover {
  transform: scale(1.1);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
