/**
 * Security Configuration for AI Hub Marketing Website
 * Implements security best practices for Angular applications
 */

// Content Security Policy Configuration
export const CSP_CONFIG = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    "'unsafe-inline'", // Required for Angular in development
    'https://fonts.googleapis.com',
    'https://www.googletagmanager.com',
    'https://www.google-analytics.com'
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'", // Required for Angular styles
    'https://fonts.googleapis.com'
  ],
  'font-src': [
    "'self'",
    'https://fonts.gstatic.com'
  ],
  'img-src': [
    "'self'",
    'data:',
    'https:',
    'blob:'
  ],
  'connect-src': [
    "'self'",
    'https://api.yourdomain.com', // Replace with your API domain
    'https://www.google-analytics.com'
  ],
  'frame-ancestors': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"]
};

// Security Headers Configuration
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
};

// Input Sanitization Patterns
export const SANITIZATION_PATTERNS = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  name: /^[a-zA-Z\s'-]{2,50}$/,
  company: /^[a-zA-Z0-9\s&.,'-]{2,100}$/,
  message: /^[\w\s.,!?'"()-]{10,1000}$/
};

// Rate Limiting Configuration
export const RATE_LIMIT_CONFIG = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // Limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false
};

// Form Validation Rules
export const FORM_VALIDATION = {
  required: {
    message: 'This field is required'
  },
  email: {
    pattern: SANITIZATION_PATTERNS.email,
    message: 'Please enter a valid email address'
  },
  minLength: (length: number) => ({
    minLength: length,
    message: `Minimum ${length} characters required`
  }),
  maxLength: (length: number) => ({
    maxLength: length,
    message: `Maximum ${length} characters allowed`
  })
};

// Trusted Domains for External Resources
export const TRUSTED_DOMAINS = [
  'fonts.googleapis.com',
  'fonts.gstatic.com',
  'www.google-analytics.com',
  'www.googletagmanager.com'
];

// Cookie Security Configuration
export const COOKIE_CONFIG = {
  httpOnly: true,
  secure: true, // Only over HTTPS
  sameSite: 'strict' as const,
  maxAge: 24 * 60 * 60 * 1000 // 24 hours
};

// API Security Configuration
export const API_CONFIG = {
  timeout: 10000, // 10 seconds
  retries: 3,
  baseURL: 'https://api.yourdomain.com', // Replace with your API
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
};

// Environment-specific security settings
export const getSecurityConfig = (environment: 'development' | 'production') => {
  const baseConfig = {
    enableCSP: true,
    enableHSTS: environment === 'production',
    enableSecurityHeaders: true,
    logSecurityEvents: true
  };

  if (environment === 'development') {
    return {
      ...baseConfig,
      allowUnsafeInline: true, // For development only
      enableSourceMaps: true
    };
  }

  return {
    ...baseConfig,
    allowUnsafeInline: false,
    enableSourceMaps: false,
    enableIntegrityChecks: true
  };
};
