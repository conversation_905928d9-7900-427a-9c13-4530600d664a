import { Component } from '@angular/core';
import { HeroSectionComponent } from '../../../components/hero-section/hero-section.component';
import { AiSolutionsComponent } from '../../../components/ai-solutions/ai-solutions.component';
import { PricingSectionComponent } from '../../../components/pricing-section/pricing-section.component';
import { DashboardPreviewComponent } from '../../../components/dashboard-preview/dashboard-preview.component';
import { AiUseCasesComponent } from '../../../components/ai-use-cases/ai-use-cases.component';
import { CtaSectionComponent } from '../../../components/cta-section/cta-section.component';

@Component({
  selector: 'app-home',
  imports: [
    HeroSectionComponent,
    AiSolutionsComponent,
    PricingSectionComponent,
    DashboardPreviewComponent,
    AiUseCasesComponent,
    CtaSectionComponent
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent {

}
