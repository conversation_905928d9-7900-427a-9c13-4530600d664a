import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company: string;
  teamSize: string;
  jobTitle: string;
  interest: string;
  message: string;
}

@Component({
  selector: 'app-contact-sales',
  imports: [CommonModule, FormsModule],
  templateUrl: './contact-sales.component.html',
  styleUrl: './contact-sales.component.css'
})
export class ContactSalesComponent {
  // Form data
  formData: ContactFormData = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    company: '',
    teamSize: '',
    jobTitle: '',
    interest: '',
    message: ''
  };

  // Form state
  isSubmitting = false;
  submitSuccess = false;
  submitError = '';

  // FAQ state
  openFaqIndex: number | null = null;

  // Contact methods
  contactMethods = [
    {
      icon: 'phone',
      title: 'Sales Hotline',
      description: 'Speak directly with our sales experts',
      contact: '+1 (800) AI-HUBS',
      availability: 'Mon-Fri, 9 AM - 6 PM EST',
      action: 'tel:******-AI-HUBS'
    },
    {
      icon: 'email',
      title: 'Sales Email',
      description: 'For detailed inquiries and proposals',
      contact: '<EMAIL>',
      availability: 'Response within 4 hours',
      action: 'mailto:<EMAIL>'
    },
    {
      icon: 'chat',
      title: 'Live Chat',
      description: 'Instant support from our team',
      contact: 'Start Chat Now',
      availability: 'Available 24/7',
      action: 'javascript:void(0)'
    }
  ];

  // Benefits
  benefits = [
    {
      title: '300% Productivity Boost',
      description: 'Average improvement reported by our enterprise clients'
    },
    {
      title: 'Enterprise-Grade Security',
      description: 'SOC 2 Type II certified with end-to-end encryption'
    },
    {
      title: '24/7 Dedicated Support',
      description: 'Priority support with dedicated customer success manager'
    },
    {
      title: 'Seamless Integration',
      description: 'Connect with 500+ tools including Slack, Microsoft Teams, and more'
    },
    {
      title: 'Custom Implementation',
      description: 'Tailored setup and training for your specific business needs'
    }
  ];

  // Sales process steps
  salesSteps = [
    {
      step: 1,
      title: 'Initial Contact',
      description: 'Fill out the form or call us. We\'ll schedule a discovery call within 24 hours.',
      color: 'primary'
    },
    {
      step: 2,
      title: 'Personalized Demo',
      description: 'See AI Hub in action with a demo tailored to your specific use cases and needs.',
      color: 'secondary'
    },
    {
      step: 3,
      title: 'Custom Proposal',
      description: 'Receive a detailed proposal with pricing, implementation plan, and timeline.',
      color: 'accent'
    },
    {
      step: 4,
      title: 'Onboarding',
      description: 'Get started with dedicated support, training, and seamless data migration.',
      color: 'warning'
    }
  ];

  // FAQ data
  faqs = [
    {
      question: 'How long does implementation take?',
      answer: 'Most implementations take 2-4 weeks depending on your team size and integration requirements. Our dedicated implementation team will work with you to ensure a smooth transition with minimal disruption to your workflow.'
    },
    {
      question: 'What\'s included in enterprise support?',
      answer: 'Enterprise support includes 24/7 priority support, dedicated customer success manager, custom training sessions, API support, and guaranteed 99.9% uptime SLA with priority incident response.'
    },
    {
      question: 'Can we integrate with our existing tools?',
      answer: 'Yes! AI Hub integrates with 500+ popular tools including Slack, Microsoft Teams, Jira, Salesforce, Google Workspace, and more. We also provide REST APIs and webhooks for custom integrations.'
    },
    {
      question: 'What security measures are in place?',
      answer: 'AI Hub is SOC 2 Type II certified with enterprise-grade security including end-to-end encryption, SSO integration, role-based access controls, audit logs, and compliance with GDPR, HIPAA, and other regulations.'
    },
    {
      question: 'Do you offer custom pricing for large teams?',
      answer: 'Absolutely! We offer volume discounts and custom pricing for teams of 100+ users. Contact our sales team to discuss your specific needs and get a personalized quote.'
    }
  ];

  // Methods
  onSubmit(): void {
    if (this.isSubmitting) return;

    this.isSubmitting = true;
    this.submitError = '';

    // Simulate API call
    setTimeout(() => {
      try {
        // Here you would typically send the data to your backend
        console.log('Form submitted:', this.formData);

        this.submitSuccess = true;
        this.resetForm();

        // Show success message for 5 seconds
        setTimeout(() => {
          this.submitSuccess = false;
        }, 5000);

      } catch (error) {
        this.submitError = 'There was an error submitting your request. Please try again.';
      } finally {
        this.isSubmitting = false;
      }
    }, 2000);
  }

  toggleFaq(index: number): void {
    this.openFaqIndex = this.openFaqIndex === index ? null : index;
  }

  private resetForm(): void {
    this.formData = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      company: '',
      teamSize: '',
      jobTitle: '',
      interest: '',
      message: ''
    };
  }

  // Utility methods
  getStepColorClass(color: string): string {
    const colorMap: { [key: string]: string } = {
      'primary': 'gradient-primary',
      'secondary': 'bg-secondary-500',
      'accent': 'bg-accent-400',
      'warning': 'bg-warning-400'
    };
    return colorMap[color] || 'gradient-primary';
  }
}
