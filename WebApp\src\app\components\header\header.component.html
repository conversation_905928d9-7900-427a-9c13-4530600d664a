<!-- Navigation Header -->
<header class="bg-white/95 backdrop-blur-md shadow-lg border-b border-neutral-200 sticky top-0 z-50">
  <div class="container-custom">
    <div class="flex justify-between items-center py-4">
      <!-- <PERSON><PERSON> & Brand -->
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center shadow-primary hover-scale">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
        </div>
        <div>
          <h1 class="text-xl font-bold text-neutral-900">AI Hub</h1>
          <p class="text-xs text-neutral-500">Intelligent Workspace</p>
        </div>
      </div>

      <!-- Desktop Navigation -->
      <nav class="hidden lg:flex items-center space-x-8">
        <div class="flex items-center space-x-6">
          <a routerLink="/home" routerLinkActive="active" class="nav-link">Home</a>
          <a routerLink="/features" routerLinkActive="active" class="nav-link">Features</a>
          <a routerLink="/company" routerLinkActive="active" class="nav-link">Company</a>
          <a routerLink="/about" routerLinkActive="active" class="nav-link">About</a>
          <a routerLink="/resources" routerLinkActive="active" class="nav-link">Resources</a>

        </div>
      </nav>

      <!-- CTA Buttons -->
      <div class="hidden md:flex items-center space-x-3">
        <a routerLink="/contact" class="btn btn-ghost btn-sm">Contact Sales</a>
        <a routerLink="/demo" class="btn btn-outline btn-sm">Watch Demo</a>
      </div>

      <!-- Mobile Menu Button -->
      <button class="md:hidden p-2 rounded-lg hover:bg-neutral-100 transition-colors">
        <svg class="w-6 h-6 text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>
  </div>
</header>
