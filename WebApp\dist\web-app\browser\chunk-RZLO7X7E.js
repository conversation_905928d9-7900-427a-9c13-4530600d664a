var Jl=Object.defineProperty,Kl=Object.defineProperties;var Xl=Object.getOwnPropertyDescriptors;var Qi=Object.getOwnPropertySymbols;var eu=Object.prototype.hasOwnProperty,tu=Object.prototype.propertyIsEnumerable;var Zi=(e,t,n)=>t in e?Jl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ne=(e,t)=>{for(var n in t||={})eu.call(t,n)&&Zi(e,n,t[n]);if(Qi)for(var n of Qi(t))tu.call(t,n)&&Zi(e,n,t[n]);return e},re=(e,t)=>Kl(e,Xl(t));function Ir(e,t){return Object.is(e,t)}var O=null,Ut=!1,Er=1,G=Symbol("SIGNAL");function E(e){let t=O;return O=e,t}function Dr(){return O}var ft={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function pt(e){if(Ut)throw new Error("");if(O===null)return;O.consumerOnSignalRead(e);let t=O.nextProducerIndex++;if(Gt(O),t<O.producerNode.length&&O.producerNode[t]!==e&&dt(O)){let n=O.producerNode[t];zt(n,O.producerIndexOfThis[t])}O.producerNode[t]!==e&&(O.producerNode[t]=e,O.producerIndexOfThis[t]=dt(O)?Ji(e,O,t):0),O.producerLastReadVersion[t]=e.version}function Yi(){Er++}function wr(e){if(!(dt(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Er)){if(!e.producerMustRecompute(e)&&!_r(e)){vr(e);return}e.producerRecomputeValue(e),vr(e)}}function br(e){if(e.liveConsumerNode===void 0)return;let t=Ut;Ut=!0;try{for(let n of e.liveConsumerNode)n.dirty||nu(n)}finally{Ut=t}}function Mr(){return O?.consumerAllowSignalWrites!==!1}function nu(e){e.dirty=!0,br(e),e.consumerMarkedDirty?.(e)}function vr(e){e.dirty=!1,e.lastCleanEpoch=Er}function Wt(e){return e&&(e.nextProducerIndex=0),E(e)}function Cr(e,t){if(E(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(dt(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)zt(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function _r(e){Gt(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(wr(n),r!==n.version))return!0}return!1}function Tr(e){if(Gt(e),dt(e))for(let t=0;t<e.producerNode.length;t++)zt(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Ji(e,t,n){if(Ki(e),e.liveConsumerNode.length===0&&Xi(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Ji(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function zt(e,t){if(Ki(e),e.liveConsumerNode.length===1&&Xi(e))for(let r=0;r<e.producerNode.length;r++)zt(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Gt(o),o.producerIndexOfThis[r]=t}}function dt(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Gt(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Ki(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Xi(e){return e.producerNode!==void 0}function xr(e,t){let n=Object.create(ru);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(wr(n),pt(n),n.value===qt)throw n.error;return n.value};return r[G]=n,r}var mr=Symbol("UNSET"),yr=Symbol("COMPUTING"),qt=Symbol("ERRORED"),ru=re(ne({},ft),{value:mr,dirty:!0,error:null,equal:Ir,kind:"computed",producerMustRecompute(e){return e.value===mr||e.value===yr},producerRecomputeValue(e){if(e.value===yr)throw new Error("Detected cycle in computations.");let t=e.value;e.value=yr;let n=Wt(e),r,o=!1;try{r=e.computation(),E(null),o=t!==mr&&t!==qt&&r!==qt&&e.equal(t,r)}catch(i){r=qt,e.error=i}finally{Cr(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function ou(){throw new Error}var es=ou;function ts(e){es(e)}function Nr(e){es=e}var iu=null;function Sr(e,t){let n=Object.create(Qt);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(pt(n),n.value);return r[G]=n,r}function ht(e,t){Mr()||ts(e),e.equal(e.value,t)||(e.value=t,su(e))}function kr(e,t){Mr()||ts(e),ht(e,t(e.value))}var Qt=re(ne({},ft),{equal:Ir,value:void 0,kind:"signal"});function su(e){e.version++,Yi(),br(e),iu?.()}function Rr(e){let t=E(null);try{return e()}finally{E(t)}}var Or;function gt(){return Or}function fe(e){let t=Or;return Or=e,t}var Zt=Symbol("NotFound");function g(e){return typeof e=="function"}function qe(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Yt=qe(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function mt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var L=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(g(r))try{r()}catch(i){t=i instanceof Yt?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{ns(i)}catch(s){t=t??[],s instanceof Yt?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Yt(t)}}add(t){var n;if(t&&t!==this)if(this.closed)ns(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&mt(n,t)}remove(t){let{_finalizers:n}=this;n&&mt(n,t),t instanceof e&&t._removeParent(this)}};L.EMPTY=(()=>{let e=new L;return e.closed=!0,e})();var Ar=L.EMPTY;function Jt(e){return e instanceof L||e&&"closed"in e&&g(e.remove)&&g(e.add)&&g(e.unsubscribe)}function ns(e){g(e)?e():e.unsubscribe()}var K={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var We={setTimeout(e,t,...n){let{delegate:r}=We;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=We;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Kt(e){We.setTimeout(()=>{let{onUnhandledError:t}=K;if(t)t(e);else throw e})}function yt(){}var rs=Pr("C",void 0,void 0);function os(e){return Pr("E",void 0,e)}function is(e){return Pr("N",e,void 0)}function Pr(e,t,n){return{kind:e,value:t,error:n}}var Te=null;function ze(e){if(K.useDeprecatedSynchronousErrorHandling){let t=!Te;if(t&&(Te={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Te;if(Te=null,n)throw r}}else e()}function ss(e){K.useDeprecatedSynchronousErrorHandling&&Te&&(Te.errorThrown=!0,Te.error=e)}var xe=class extends L{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Jt(t)&&t.add(this)):this.destination=fu}static create(t,n,r){return new Ge(t,n,r)}next(t){this.isStopped?Fr(is(t),this):this._next(t)}error(t){this.isStopped?Fr(os(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Fr(rs,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},uu=Function.prototype.bind;function Lr(e,t){return uu.call(e,t)}var jr=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Xt(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Xt(r)}else Xt(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Xt(n)}}},Ge=class extends xe{constructor(t,n,r){super();let o;if(g(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&K.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Lr(t.next,i),error:t.error&&Lr(t.error,i),complete:t.complete&&Lr(t.complete,i)}):o=t}this.destination=new jr(o)}};function Xt(e){K.useDeprecatedSynchronousErrorHandling?ss(e):Kt(e)}function du(e){throw e}function Fr(e,t){let{onStoppedNotification:n}=K;n&&We.setTimeout(()=>n(e,t))}var fu={closed:!0,next:yt,error:du,complete:yt};var Qe=typeof Symbol=="function"&&Symbol.observable||"@@observable";function W(e){return e}function pu(...e){return Vr(e)}function Vr(e){return e.length===0?W:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var C=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=gu(n)?n:new Ge(n,r,o);return ze(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=as(r),new r((o,i)=>{let s=new Ge({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Qe](){return this}pipe(...n){return Vr(n)(this)}toPromise(n){return n=as(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function as(e){var t;return(t=e??K.Promise)!==null&&t!==void 0?t:Promise}function hu(e){return e&&g(e.next)&&g(e.error)&&g(e.complete)}function gu(e){return e&&e instanceof xe||hu(e)&&Jt(e)}function Hr(e){return g(e?.lift)}function w(e){return t=>{if(Hr(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function D(e,t,n,r,o){return new Br(e,t,n,r,o)}var Br=class extends xe{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function $r(){return w((e,t)=>{let n=null;e._refCount++;let r=D(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Ur=class extends C{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Hr(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new L;let n=this.getSubject();t.add(this.source.subscribe(D(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=L.EMPTY)}return t}refCount(){return $r()(this)}};var cs=qe(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Ie=(()=>{class e extends C{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new en(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new cs}next(n){ze(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){ze(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){ze(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Ar:(this.currentObservers=null,i.push(n),new L(()=>{this.currentObservers=null,mt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new C;return n.source=this,n}}return e.create=(t,n)=>new en(t,n),e})(),en=class extends Ie{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Ar}};var vt=class extends Ie{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var It=new C(e=>e.complete());function ls(e){return e&&g(e.schedule)}function us(e){return e[e.length-1]}function tn(e){return g(us(e))?e.pop():void 0}function Ee(e){return ls(us(e))?e.pop():void 0}function fs(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(f){s(f)}}function c(u){try{l(r.throw(u))}catch(f){s(f)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function ds(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Ne(e){return this instanceof Ne?(this.v=e,this):new Ne(e)}function ps(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(h){return Promise.resolve(h).then(d,f)}}function a(d,h){r[d]&&(o[d]=function(M){return new Promise(function(P,k){i.push([d,M,P,k])>1||c(d,M)})},h&&(o[d]=h(o[d])))}function c(d,h){try{l(r[d](h))}catch(M){p(i[0][3],M)}}function l(d){d.value instanceof Ne?Promise.resolve(d.value.v).then(u,f):p(i[0][2],d)}function u(d){c("next",d)}function f(d){c("throw",d)}function p(d,h){d(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function hs(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof ds=="function"?ds(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var nn=e=>e&&typeof e.length=="number"&&typeof e!="function";function rn(e){return g(e?.then)}function on(e){return g(e[Qe])}function sn(e){return Symbol.asyncIterator&&g(e?.[Symbol.asyncIterator])}function an(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function mu(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var cn=mu();function ln(e){return g(e?.[cn])}function un(e){return ps(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Ne(n.read());if(o)return yield Ne(void 0);yield yield Ne(r)}}finally{n.releaseLock()}})}function dn(e){return g(e?.getReader)}function R(e){if(e instanceof C)return e;if(e!=null){if(on(e))return yu(e);if(nn(e))return vu(e);if(rn(e))return Iu(e);if(sn(e))return gs(e);if(ln(e))return Eu(e);if(dn(e))return Du(e)}throw an(e)}function yu(e){return new C(t=>{let n=e[Qe]();if(g(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function vu(e){return new C(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Iu(e){return new C(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Kt)})}function Eu(e){return new C(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function gs(e){return new C(t=>{wu(e,t).catch(n=>t.error(n))})}function Du(e){return gs(un(e))}function wu(e,t){var n,r,o,i;return fs(this,void 0,void 0,function*(){try{for(n=hs(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function B(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function fn(e,t=0){return w((n,r)=>{n.subscribe(D(r,o=>B(r,e,()=>r.next(o),t),()=>B(r,e,()=>r.complete(),t),o=>B(r,e,()=>r.error(o),t)))})}function pn(e,t=0){return w((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function ms(e,t){return R(e).pipe(pn(t),fn(t))}function ys(e,t){return R(e).pipe(pn(t),fn(t))}function vs(e,t){return new C(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Is(e,t){return new C(n=>{let r;return B(n,t,()=>{r=e[cn](),B(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>g(r?.return)&&r.return()})}function hn(e,t){if(!e)throw new Error("Iterable cannot be null");return new C(n=>{B(n,t,()=>{let r=e[Symbol.asyncIterator]();B(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Es(e,t){return hn(un(e),t)}function Ds(e,t){if(e!=null){if(on(e))return ms(e,t);if(nn(e))return vs(e,t);if(rn(e))return ys(e,t);if(sn(e))return hn(e,t);if(ln(e))return Is(e,t);if(dn(e))return Es(e,t)}throw an(e)}function De(e,t){return t?Ds(e,t):R(e)}function bu(...e){let t=Ee(e);return De(e,t)}function Mu(e,t){let n=g(e)?e:()=>e,r=o=>o.error(n());return new C(t?o=>t.schedule(r,0,o):r)}function Cu(e){return!!e&&(e instanceof C||g(e.lift)&&g(e.subscribe))}var Se=qe(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function ke(e,t){return w((n,r)=>{let o=0;n.subscribe(D(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:_u}=Array;function Tu(e,t){return _u(t)?e(...t):e(t)}function gn(e){return ke(t=>Tu(e,t))}var{isArray:xu}=Array,{getPrototypeOf:Nu,prototype:Su,keys:ku}=Object;function mn(e){if(e.length===1){let t=e[0];if(xu(t))return{args:t,keys:null};if(Ru(t)){let n=ku(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Ru(e){return e&&typeof e=="object"&&Nu(e)===Su}function yn(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Ou(...e){let t=Ee(e),n=tn(e),{args:r,keys:o}=mn(e);if(r.length===0)return De([],t);let i=new C(Au(r,t,o?s=>yn(o,s):W));return n?i.pipe(gn(n)):i}function Au(e,t,n=W){return r=>{ws(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)ws(t,()=>{let l=De(e[c],t),u=!1;l.subscribe(D(r,f=>{i[c]=f,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ws(e,t,n){e?B(n,e,t):t()}function bs(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,f=!1,p=()=>{f&&!c.length&&!l&&t.complete()},d=M=>l<r?h(M):c.push(M),h=M=>{i&&t.next(M),l++;let P=!1;R(n(M,u++)).subscribe(D(t,k=>{o?.(k),i?d(k):t.next(k)},()=>{P=!0},void 0,()=>{if(P)try{for(l--;c.length&&l<r;){let k=c.shift();s?B(t,s,()=>h(k)):h(k)}p()}catch(k){t.error(k)}}))};return e.subscribe(D(t,d,()=>{f=!0,p()})),()=>{a?.()}}function Re(e,t,n=1/0){return g(t)?Re((r,o)=>ke((i,s)=>t(r,i,o,s))(R(e(r,o))),n):(typeof t=="number"&&(n=t),w((r,o)=>bs(r,o,e,n)))}function qr(e=1/0){return Re(W,e)}function Ms(){return qr(1)}function vn(...e){return Ms()(De(e,Ee(e)))}function Pu(e){return new C(t=>{R(e()).subscribe(t)})}function Lu(...e){let t=tn(e),{args:n,keys:r}=mn(e),o=new C(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let f=!1;R(n[u]).subscribe(D(i,p=>{f||(f=!0,l--),a[u]=p},()=>c--,void 0,()=>{(!c||!f)&&(l||i.next(r?yn(r,a):a),i.complete())}))}});return t?o.pipe(gn(t)):o}function Et(e,t){return w((n,r)=>{let o=0;n.subscribe(D(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Cs(e){return w((t,n)=>{let r=null,o=!1,i;r=t.subscribe(D(n,void 0,void 0,s=>{i=R(e(s,Cs(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function _s(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(D(s,u=>{let f=l++;c=a?e(c,u,f):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Fu(e,t){return g(t)?Re(e,t,1):Re(e,1)}function Dt(e){return w((t,n)=>{let r=!1;t.subscribe(D(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Wr(e){return e<=0?()=>It:w((t,n)=>{let r=0;t.subscribe(D(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function In(e=ju){return w((t,n)=>{let r=!1;t.subscribe(D(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function ju(){return new Se}function Vu(e){return w((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Hu(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Et((o,i)=>e(o,i,r)):W,Wr(1),n?Dt(t):In(()=>new Se))}function zr(e){return e<=0?()=>It:w((t,n)=>{let r=[];t.subscribe(D(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Bu(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Et((o,i)=>e(o,i,r)):W,zr(1),n?Dt(t):In(()=>new Se))}function $u(e,t){return w(_s(e,t,arguments.length>=2,!0))}function Uu(...e){let t=Ee(e);return w((n,r)=>{(t?vn(e,n,t):vn(e,n)).subscribe(r)})}function qu(e,t){return w((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(D(r,c=>{o?.unsubscribe();let l=0,u=i++;R(e(c,u)).subscribe(o=D(r,f=>r.next(t?t(c,f,u,l++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Wu(e){return w((t,n)=>{R(e).subscribe(D(n,()=>n.complete(),yt)),!n.closed&&t.subscribe(n)})}function zu(e,t,n){let r=g(e)||t||n?{next:e,error:t,complete:n}:e;return r?w((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(D(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):W}var Ea="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",b=class extends Error{code;constructor(t,n){super(Qu(t,n)),this.code=t}};function Gu(e){return`NG0${Math.abs(e)}`}function Qu(e,t){return`${Gu(e)}${t?": "+t:""}`}var Da=Symbol("InputSignalNode#UNSET"),Zu=re(ne({},Qt),{transformFn:void 0,applyValueToInputSignal(e,t){ht(e,t)}});function wa(e,t){let n=Object.create(Zu);n.value=e,n.transformFn=t?.transform;function r(){if(pt(n),n.value===Da){let o=null;throw new b(-950,o)}return n.value}return r[G]=n,r}function jt(e){return{toString:e}.toString()}var En="__parameters__";function Yu(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function ba(e,t,n){return jt(()=>{let r=Yu(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let f=c.hasOwnProperty(En)?c[En]:Object.defineProperty(c,En,{value:[]})[En];for(;f.length<=u;)f.push(null);return(f[u]=f[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var kn=globalThis;function x(e){for(let t in e)if(e[t]===x)return t;throw Error("Could not find renamed property on target object.")}function Ju(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function $(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map($).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Ts(e,t){return e?t?`${e} ${t}`:e:t||""}var Ku=x({__forward_ref__:x});function Ma(e){return e.__forward_ref__=Ma,e.toString=function(){return $(this())},e}function j(e){return Ca(e)?e():e}function Ca(e){return typeof e=="function"&&e.hasOwnProperty(Ku)&&e.__forward_ref__===Ma}function V(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function nb(e){return{providers:e.providers||[],imports:e.imports||[]}}function or(e){return xs(e,_a)||xs(e,Ta)}function rb(e){return or(e)!==null}function xs(e,t){return e.hasOwnProperty(t)?e[t]:null}function Xu(e){let t=e&&(e[_a]||e[Ta]);return t||null}function Ns(e){return e&&(e.hasOwnProperty(Ss)||e.hasOwnProperty(ed))?e[Ss]:null}var _a=x({\u0275prov:x}),Ss=x({\u0275inj:x}),Ta=x({ngInjectableDef:x}),ed=x({ngInjectorDef:x}),S=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=V({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function xa(e){return e&&!!e.\u0275providers}var td=x({\u0275cmp:x}),nd=x({\u0275dir:x}),rd=x({\u0275pipe:x}),od=x({\u0275mod:x}),Rn=x({\u0275fac:x}),Ct=x({__NG_ELEMENT_ID__:x}),ks=x({__NG_ENV_ID__:x});function Xe(e){return typeof e=="string"?e:e==null?"":String(e)}function id(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Xe(e)}function Na(e,t){throw new b(-200,e)}function di(e,t){throw new b(-201,!1)}var v=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(v||{}),ao;function Sa(){return ao}function Q(e){let t=ao;return ao=e,t}function ka(e,t,n){let r=or(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&v.Optional)return null;if(t!==void 0)return t;di(e,"Injector")}var sd={},Oe=sd,co="__NG_DI_FLAG__",On=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?Zt:Oe,r)}},An="ngTempTokenPath",ad="ngTokenPath",cd=/\n/gm,ld="\u0275",Rs="__source";function ud(e,t=v.Default){if(gt()===void 0)throw new b(-203,!1);if(gt()===null)return ka(e,void 0,t);{let n=gt(),r;return n instanceof On?r=n.injector:r=n,r.get(e,t&v.Optional?null:void 0,t)}}function Ce(e,t=v.Default){return(Sa()||ud)(j(e),t)}function T(e,t=v.Default){return Ce(e,ir(t))}function ir(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function lo(e){let t=[];for(let n=0;n<e.length;n++){let r=j(e[n]);if(Array.isArray(r)){if(r.length===0)throw new b(900,!1);let o,i=v.Default;for(let s=0;s<r.length;s++){let a=r[s],c=dd(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(Ce(o,i))}else t.push(Ce(r))}return t}function Ra(e,t){return e[co]=t,e.prototype[co]=t,e}function dd(e){return e[co]}function fd(e,t,n,r){let o=e[An];throw t[Rs]&&o.unshift(t[Rs]),e.message=pd(`
`+e.message,o,n,r),e[ad]=o,e[An]=null,e}function pd(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==ld?e.slice(2):e;let o=$(t);if(Array.isArray(t))o=t.map($).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):$(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(cd,`
  `)}`}var hd=Ra(ba("Optional"),8);var gd=Ra(ba("SkipSelf"),4);function et(e,t){let n=e.hasOwnProperty(Rn);return n?e[Rn]:null}function md(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function yd(e){return e.flat(Number.POSITIVE_INFINITY)}function fi(e,t){e.forEach(n=>Array.isArray(n)?fi(n,t):t(n))}function Oa(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Pn(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function vd(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function Id(e,t,n){let r=Vt(e,t);return r>=0?e[r|1]=n:(r=~r,vd(e,r,t,n)),r}function Gr(e,t){let n=Vt(e,t);if(n>=0)return e[n|1]}function Vt(e,t){return Ed(e,t,1)}function Ed(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Pe={},Z=[],Ln=new S(""),Aa=new S("",-1),Pa=new S(""),Fn=class{get(t,n=Oe){if(n===Oe){let r=new Error(`NullInjectorError: No provider for ${$(t)}!`);throw r.name="NullInjectorError",r}return n}};function La(e,t){let n=e[od]||null;if(!n&&t===!0)throw new Error(`Type ${$(e)} does not have '\u0275mod' property.`);return n}function tt(e){return e[td]||null}function Dd(e){return e[nd]||null}function wd(e){return e[rd]||null}function bd(e){return{\u0275providers:e}}function Md(...e){return{\u0275providers:Fa(!0,e),\u0275fromNgModule:!0}}function Fa(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return fi(t,s=>{let a=s;uo(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&ja(o,i),n}function ja(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];pi(o,i=>{t(i,r)})}}function uo(e,t,n,r){if(e=j(e),!e)return!1;let o=null,i=Ns(e),s=!i&&tt(e);if(!i&&!s){let c=e.ngModule;if(i=Ns(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)uo(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{fi(i.imports,u=>{uo(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&ja(l,t)}if(!a){let l=et(o)||(()=>new o);t({provide:o,useFactory:l,deps:Z},o),t({provide:Pa,useValue:o,multi:!0},o),t({provide:Ln,useValue:()=>Ce(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;pi(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function pi(e,t){for(let n of e)xa(n)&&(n=n.\u0275providers),Array.isArray(n)?pi(n,t):t(n)}var Cd=x({provide:String,useValue:x});function Va(e){return e!==null&&typeof e=="object"&&Cd in e}function _d(e){return!!(e&&e.useExisting)}function Td(e){return!!(e&&e.useFactory)}function nt(e){return typeof e=="function"}function xd(e){return!!e.useClass}var Ha=new S(""),Cn={},Os={},Qr;function hi(){return Qr===void 0&&(Qr=new Fn),Qr}var _e=class{},_t=class extends _e{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,po(t,s=>this.processProvider(s)),this.records.set(Aa,Ze(void 0,this)),o.has("environment")&&this.records.set(_e,Ze(void 0,this));let i=this.records.get(Ha);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Pa,Z,v.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?Zt:Oe,r)}destroy(){bt(this),this._destroyed=!0;let t=E(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),E(t)}}onDestroy(t){return bt(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){bt(this);let n=fe(this),r=Q(void 0),o;try{return t()}finally{fe(n),Q(r)}}get(t,n=Oe,r=v.Default){if(bt(this),t.hasOwnProperty(ks))return t[ks](this);r=ir(r);let o,i=fe(this),s=Q(void 0);try{if(!(r&v.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=Od(t)&&or(t);l&&this.injectableDefInScope(l)?c=Ze(fo(t),Cn):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&v.Self?hi():this.parent;return n=r&v.Optional&&n===Oe?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[An]=a[An]||[]).unshift($(t)),i)throw a;return fd(a,t,"R3InjectorError",this.source)}else throw a}finally{Q(s),fe(i)}}resolveInjectorInitializers(){let t=E(null),n=fe(this),r=Q(void 0),o;try{let i=this.get(Ln,Z,v.Self);for(let s of i)s()}finally{fe(n),Q(r),E(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push($(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=j(t);let n=nt(t)?t:j(t&&t.provide),r=Sd(t);if(!nt(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Ze(void 0,Cn,!0),o.factory=()=>lo(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=E(null);try{return n.value===Os?Na($(t)):n.value===Cn&&(n.value=Os,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&Rd(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{E(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=j(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function fo(e){let t=or(e),n=t!==null?t.factory:et(e);if(n!==null)return n;if(e instanceof S)throw new b(204,!1);if(e instanceof Function)return Nd(e);throw new b(204,!1)}function Nd(e){if(e.length>0)throw new b(204,!1);let n=Xu(e);return n!==null?()=>n.factory(e):()=>new e}function Sd(e){if(Va(e))return Ze(void 0,e.useValue);{let t=Ba(e);return Ze(t,Cn)}}function Ba(e,t,n){let r;if(nt(e)){let o=j(e);return et(o)||fo(o)}else if(Va(e))r=()=>j(e.useValue);else if(Td(e))r=()=>e.useFactory(...lo(e.deps||[]));else if(_d(e))r=(o,i)=>Ce(j(e.useExisting),i!==void 0&&i&v.Optional?v.Optional:void 0);else{let o=j(e&&(e.useClass||e.provide));if(kd(e))r=()=>new o(...lo(e.deps));else return et(o)||fo(o)}return r}function bt(e){if(e.destroyed)throw new b(205,!1)}function Ze(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function kd(e){return!!e.deps}function Rd(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Od(e){return typeof e=="function"||typeof e=="object"&&e instanceof S}function po(e,t){for(let n of e)Array.isArray(n)?po(n,t):n&&xa(n)?po(n.\u0275providers,t):t(n)}function $a(e,t){let n;e instanceof _t?(bt(e),n=e):n=new On(e);let r,o=fe(n),i=Q(void 0);try{return t()}finally{fe(o),Q(i)}}function Ad(){return Sa()!==void 0||gt()!=null}function Pd(e){return typeof e=="function"}var me=0,I=1,m=2,F=3,ee=4,te=5,jn=6,Vn=7,U=8,rt=9,pe=10,A=11,Tt=12,As=13,at=14,ie=15,Le=16,Ye=17,he=18,sr=19,Ua=20,be=21,Zr=22,Hn=23,Y=24,Yr=25,se=26,qa=1;var Fe=7,Bn=8,ot=9,z=10;function Me(e){return Array.isArray(e)&&typeof e[qa]=="object"}function ye(e){return Array.isArray(e)&&e[qa]===!0}function Wa(e){return(e.flags&4)!==0}function ct(e){return e.componentOffset>-1}function gi(e){return(e.flags&1)===1}function ae(e){return!!e.template}function $n(e){return(e[m]&512)!==0}function lt(e){return(e[m]&256)===256}var ho=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function za(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var ob=(()=>{let e=()=>Ga;return e.ngInherit=!0,e})();function Ga(e){return e.type.prototype.ngOnChanges&&(e.setInput=Fd),Ld}function Ld(){let e=Za(this),t=e?.current;if(t){let n=e.previous;if(n===Pe)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Fd(e,t,n,r,o){let i=this.declaredInputs[r],s=Za(e)||jd(e,{previous:Pe,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new ho(l&&l.currentValue,n,c===Pe),za(e,t,o,n)}var Qa="__ngSimpleChanges__";function Za(e){return e[Qa]||null}function jd(e,t){return e[Qa]=t}var Ps=null;var N=function(e,t=null,n){Ps?.(e,t,n)},Ya="svg",Vd="math";function ce(e){for(;Array.isArray(e);)e=e[me];return e}function Ja(e,t){return ce(t[e])}function ue(e,t){return ce(t[e.index])}function Ka(e,t){return e.data[t]}function Hd(e,t){return e[t]}function le(e,t){let n=t[e];return Me(n)?n:n[me]}function Bd(e){return(e[m]&4)===4}function mi(e){return(e[m]&128)===128}function $d(e){return ye(e[F])}function Un(e,t){return t==null?null:e[t]}function Xa(e){e[Ye]=0}function ec(e){e[m]&1024||(e[m]|=1024,mi(e)&&cr(e))}function Ud(e,t){for(;e>0;)t=t[at],e--;return t}function ar(e){return!!(e[m]&9216||e[Y]?.dirty)}function go(e){e[pe].changeDetectionScheduler?.notify(8),e[m]&64&&(e[m]|=1024),ar(e)&&cr(e)}function cr(e){e[pe].changeDetectionScheduler?.notify(0);let t=je(e);for(;t!==null&&!(t[m]&8192||(t[m]|=8192,!mi(t)));)t=je(t)}function tc(e,t){if(lt(e))throw new b(911,!1);e[be]===null&&(e[be]=[]),e[be].push(t)}function qd(e,t){if(e[be]===null)return;let n=e[be].indexOf(t);n!==-1&&e[be].splice(n,1)}function je(e){let t=e[F];return ye(t)?t[F]:t}function yi(e){return e[Vn]??=[]}function vi(e){return e.cleanup??=[]}function Wd(e,t,n,r){let o=yi(t);o.push(n),e.firstCreatePass&&vi(e).push(r,o.length-1)}var y={lFrame:dc(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var mo=!1;function zd(){return y.lFrame.elementDepthCount}function Gd(){y.lFrame.elementDepthCount++}function Qd(){y.lFrame.elementDepthCount--}function nc(){return y.bindingsEnabled}function Zd(){return y.skipHydrationRootTNode!==null}function Yd(e){return y.skipHydrationRootTNode===e}function Jd(){y.skipHydrationRootTNode=null}function _(){return y.lFrame.lView}function H(){return y.lFrame.tView}function ib(e){return y.lFrame.contextLView=e,e[U]}function sb(e){return y.lFrame.contextLView=null,e}function q(){let e=rc();for(;e!==null&&e.type===64;)e=e.parent;return e}function rc(){return y.lFrame.currentTNode}function Kd(){let e=y.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Ht(e,t){let n=y.lFrame;n.currentTNode=e,n.isParent=t}function oc(){return y.lFrame.isParent}function Xd(){y.lFrame.isParent=!1}function ef(){return y.lFrame.contextLView}function ic(){return mo}function Ls(e){let t=mo;return mo=e,t}function sc(){let e=y.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function tf(){return y.lFrame.bindingIndex}function nf(e){return y.lFrame.bindingIndex=e}function lr(){return y.lFrame.bindingIndex++}function ac(e){let t=y.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function rf(){return y.lFrame.inI18n}function of(e,t){let n=y.lFrame;n.bindingIndex=n.bindingRootIndex=e,yo(t)}function sf(){return y.lFrame.currentDirectiveIndex}function yo(e){y.lFrame.currentDirectiveIndex=e}function af(e){let t=y.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function cc(){return y.lFrame.currentQueryIndex}function Ii(e){y.lFrame.currentQueryIndex=e}function cf(e){let t=e[I];return t.type===2?t.declTNode:t.type===1?e[te]:null}function lc(e,t,n){if(n&v.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&v.Host);)if(o=cf(i),o===null||(i=i[at],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=y.lFrame=uc();return r.currentTNode=t,r.lView=e,!0}function Ei(e){let t=uc(),n=e[I];y.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function uc(){let e=y.lFrame,t=e===null?null:e.child;return t===null?dc(e):t}function dc(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function fc(){let e=y.lFrame;return y.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var pc=fc;function Di(){let e=fc();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function lf(e){return(y.lFrame.contextLView=Ud(e,y.lFrame.contextLView))[U]}function Ue(){return y.lFrame.selectedIndex}function Ve(e){y.lFrame.selectedIndex=e}function wi(){let e=y.lFrame;return Ka(e.tView,e.selectedIndex)}function ab(){y.lFrame.currentNamespace=Ya}function cb(){uf()}function uf(){y.lFrame.currentNamespace=null}function df(){return y.lFrame.currentNamespace}var hc=!0;function bi(){return hc}function Mi(e){hc=e}function ff(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Ga(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function gc(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function _n(e,t,n){mc(e,t,3,n)}function Tn(e,t,n,r){(e[m]&3)===n&&mc(e,t,n,r)}function Jr(e,t){let n=e[m];(n&3)===t&&(n&=16383,n+=1,e[m]=n)}function mc(e,t,n,r){let o=r!==void 0?e[Ye]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Ye]+=65536),(a<i||i==-1)&&(pf(e,n,t,c),e[Ye]=(e[Ye]&**********)+c+2),c++}function Fs(e,t){N(4,e,t);let n=E(null);try{t.call(e)}finally{E(n),N(5,e,t)}}function pf(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[m]>>14<e[Ye]>>16&&(e[m]&3)===t&&(e[m]+=16384,Fs(a,i)):Fs(a,i)}var Ke=-1,He=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function hf(e){return(e.flags&8)!==0}function gf(e){return(e.flags&16)!==0}function mf(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];yf(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function yc(e){return e===3||e===4||e===6}function yf(e){return e.charCodeAt(0)===64}function xt(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?js(e,n,o,null,t[++r]):js(e,n,o,null,null))}}return e}function js(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function vc(e){return e!==Ke}function qn(e){return e&32767}function vf(e){return e>>16}function Wn(e,t){let n=vf(e),r=t;for(;n>0;)r=r[at],n--;return r}var vo=!0;function Vs(e){let t=vo;return vo=e,t}var If=256,Ic=If-1,Ec=5,Ef=0,oe={};function Df(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Ct)&&(r=n[Ct]),r==null&&(r=n[Ct]=Ef++);let o=r&Ic,i=1<<o;t.data[e+(o>>Ec)]|=i}function zn(e,t){let n=Dc(e,t);if(n!==-1)return n;let r=t[I];r.firstCreatePass&&(e.injectorIndex=t.length,Kr(r.data,e),Kr(t,null),Kr(r.blueprint,null));let o=Ci(e,t),i=e.injectorIndex;if(vc(o)){let s=qn(o),a=Wn(o,t),c=a[I].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Kr(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Dc(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Ci(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=_c(o),r===null)return Ke;if(n++,o=o[at],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Ke}function Io(e,t,n){Df(e,t,n)}function wf(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(yc(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function wc(e,t,n){if(n&v.Optional||e!==void 0)return e;di(t,"NodeInjector")}function bc(e,t,n,r){if(n&v.Optional&&r===void 0&&(r=null),(n&(v.Self|v.Host))===0){let o=e[rt],i=Q(void 0);try{return o?o.get(t,r,n&v.Optional):ka(t,r,n&v.Optional)}finally{Q(i)}}return wc(r,t,n)}function Mc(e,t,n,r=v.Default,o){if(e!==null){if(t[m]&2048&&!(r&v.Self)){let s=_f(e,t,n,r,oe);if(s!==oe)return s}let i=Cc(e,t,n,r,oe);if(i!==oe)return i}return bc(t,n,r,o)}function Cc(e,t,n,r,o){let i=Mf(n);if(typeof i=="function"){if(!lc(t,e,r))return r&v.Host?wc(o,n,r):bc(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&v.Optional))di(n);else return s}finally{pc()}}else if(typeof i=="number"){let s=null,a=Dc(e,t),c=Ke,l=r&v.Host?t[ie][te]:null;for((a===-1||r&v.SkipSelf)&&(c=a===-1?Ci(e,t):t[a+8],c===Ke||!Bs(r,!1)?a=-1:(s=t[I],a=qn(c),t=Wn(c,t)));a!==-1;){let u=t[I];if(Hs(i,a,u.data)){let f=bf(a,t,n,s,r,l);if(f!==oe)return f}c=t[a+8],c!==Ke&&Bs(r,t[I].data[a+8]===l)&&Hs(i,a,t)?(s=u,a=qn(c),t=Wn(c,t)):a=-1}}return o}function bf(e,t,n,r,o,i){let s=t[I],a=s.data[e+8],c=r==null?ct(a)&&vo:r!=s&&(a.type&3)!==0,l=o&v.Host&&i===a,u=xn(a,s,n,c,l);return u!==null?Nt(t,s,u,a,o):oe}function xn(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,f=r?a:a+u,p=o?a+u:l;for(let d=f;d<p;d++){let h=s[d];if(d<c&&n===h||d>=c&&h.type===n)return d}if(o){let d=s[c];if(d&&ae(d)&&d.type===n)return c}return null}function Nt(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof He){let a=i;a.resolving&&Na(id(s[n]));let c=Vs(a.canSeeViewProviders);a.resolving=!0;let l,u=a.injectImpl?Q(a.injectImpl):null,f=lc(e,r,v.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&ff(n,s[n],t)}finally{u!==null&&Q(u),Vs(c),a.resolving=!1,pc()}}return i}function Mf(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Ct)?e[Ct]:void 0;return typeof t=="number"?t>=0?t&Ic:Cf:t}function Hs(e,t,n){let r=1<<e;return!!(n[t+(e>>Ec)]&r)}function Bs(e,t){return!(e&v.Self)&&!(e&v.Host&&t)}var Ae=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Mc(this._tNode,this._lView,t,ir(r),n)}};function Cf(){return new Ae(q(),_())}function lb(e){return jt(()=>{let t=e.prototype.constructor,n=t[Rn]||Eo(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Rn]||Eo(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Eo(e){return Ca(e)?()=>{let t=Eo(j(e));return t&&t()}:et(e)}function _f(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[m]&2048&&!$n(s);){let a=Cc(i,s,n,r|v.Self,oe);if(a!==oe)return a;let c=i.parent;if(!c){let l=s[Ua];if(l){let u=l.get(n,oe,r);if(u!==oe)return u}c=_c(s),s=s[at]}i=c}return o}function _c(e){let t=e[I],n=t.type;return n===2?t.declTNode:n===1?e[te]:null}function ub(e){return wf(q(),e)}function $s(e,t=null,n=null,r){let o=Tc(e,t,n,r);return o.resolveInjectorInitializers(),o}function Tc(e,t=null,n=null,r,o=new Set){let i=[n||Z,Md(e)];return r=r||(typeof e=="object"?void 0:$(e)),new _t(i,t||hi(),r||null,o)}var St=class e{static THROW_IF_NOT_FOUND=Oe;static NULL=new Fn;static create(t,n){if(Array.isArray(t))return $s({name:""},n,t,"");{let r=t.name??"";return $s({name:r},t.parent,t.providers,r)}}static \u0275prov=V({token:e,providedIn:"any",factory:()=>Ce(Aa)});static __NG_ELEMENT_ID__=-1};var Tf=new S("");Tf.__NG_ELEMENT_ID__=e=>{let t=q();if(t===null)throw new b(204,!1);if(t.type&2)return t.value;if(e&v.Optional)return null;throw new b(204,!1)};var xc=!1,Nc=(()=>{class e{static __NG_ELEMENT_ID__=xf;static __NG_ENV_ID__=n=>n}return e})(),Do=class extends Nc{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return lt(n)?(t(),()=>{}):(tc(n,t),()=>qd(n,t))}};function xf(){return new Do(_())}var kt=class{},_i=new S("",{providedIn:"root",factory:()=>!1});var Sc=new S(""),kc=new S(""),ur=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new vt(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=V({token:e,providedIn:"root",factory:()=>new e})}return e})();var wo=class extends Ie{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Ad()&&(this.destroyRef=T(Nc,{optional:!0})??void 0,this.pendingTasks=T(ur,{optional:!0})??void 0)}emit(t){let n=E(null);try{super.next(t)}finally{E(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof L&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},we=wo;function Gn(...e){}function Rc(e){let t,n;function r(){e=Gn;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Us(e){return queueMicrotask(()=>e()),()=>{e=Gn}}var Ti="isAngularZone",Qn=Ti+"_ID",Nf=0,J=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new we(!1);onMicrotaskEmpty=new we(!1);onStable=new we(!1);onError=new we(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=xc}=t;if(typeof Zone>"u")throw new b(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Rf(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Ti)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new b(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new b(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Sf,Gn,Gn);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Sf={};function xi(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function kf(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Rc(()=>{e.callbackScheduled=!1,bo(e),e.isCheckStableRunning=!0,xi(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),bo(e)}function Rf(e){let t=()=>{kf(e)},n=Nf++;e._inner=e._inner.fork({name:"angular",properties:{[Ti]:!0,[Qn]:n,[Qn+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Of(c))return r.invokeTask(i,s,a,c);try{return qs(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Ws(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return qs(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Af(c)&&t(),Ws(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,bo(e),xi(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function bo(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function qs(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Ws(e){e._nesting--,xi(e)}var Mo=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new we;onMicrotaskEmpty=new we;onStable=new we;onError=new we;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function Of(e){return Oc(e,"__ignore_ng_zone__")}function Af(e){return Oc(e,"__scheduler_tick__")}function Oc(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var it=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Pf=new S("",{providedIn:"root",factory:()=>{let e=T(J),t=T(it);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function zs(e,t){return wa(e,t)}function Lf(e){return wa(Da,e)}var db=(zs.required=Lf,zs);function Ff(){return ut(q(),_())}function ut(e,t){return new dr(ue(e,t))}var dr=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Ff}return e})();function jf(e){return e instanceof dr?e.nativeElement:e}function Vf(e){return typeof e=="function"&&e[G]!==void 0}function fb(e,t){let n=Sr(e,t?.equal),r=n[G];return n.set=o=>ht(r,o),n.update=o=>kr(r,o),n.asReadonly=Hf.bind(n),n}function Hf(){let e=this[G];if(e.readonlyFn===void 0){let t=()=>this();t[G]=e,e.readonlyFn=t}return e.readonlyFn}function Ac(e){return Vf(e)&&typeof e.set=="function"}function Bf(){return this._results[Symbol.iterator]()}var Co=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new Ie}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=yd(t);(this._changesDetected=!md(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=Bf};function Pc(e){return(e.flags&128)===128}var Lc=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Lc||{}),Fc=new Map,$f=0;function Uf(){return $f++}function qf(e){Fc.set(e[sr],e)}function _o(e){Fc.delete(e[sr])}var Gs="__ngContext__";function Bt(e,t){Me(t)?(e[Gs]=t[sr],qf(t)):e[Gs]=t}function jc(e){return Hc(e[Tt])}function Vc(e){return Hc(e[ee])}function Hc(e){for(;e!==null&&!ye(e);)e=e[ee];return e}var To;function pb(e){To=e}function Wf(){if(To!==void 0)return To;if(typeof document<"u")return document;throw new b(210,!1)}var hb=new S("",{providedIn:"root",factory:()=>zf}),zf="ng",Gf=new S(""),gb=new S("",{providedIn:"platform",factory:()=>"unknown"});var mb=new S("",{providedIn:"root",factory:()=>Wf().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Qf="h",Zf="b";var Bc=!1,Yf=new S("",{providedIn:"root",factory:()=>Bc});var $c=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}($c||{}),Uc=new S(""),Qs=new Set;function qc(e){Qs.has(e)||(Qs.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Jf=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=V({token:e,providedIn:"root",factory:()=>new e})}return e})();var Kf=(e,t,n,r)=>{};function Xf(e,t,n,r){Kf(e,t,n,r)}var ep=()=>null;function Wc(e,t,n=!1){return ep(e,t,n)}function zc(e,t){let n=e.contentQueries;if(n!==null){let r=E(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Ii(i),a.contentQueries(2,t[s],s)}}}finally{E(r)}}}function xo(e,t,n){Ii(0);let r=E(null);try{t(e,n)}finally{E(r)}}function Gc(e,t,n){if(Wa(t)){let r=E(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{E(r)}}}var Rt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Rt||{}),Dn;function tp(){if(Dn===void 0&&(Dn=null,kn.trustedTypes))try{Dn=kn.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Dn}function fr(e){return tp()?.createHTML(e)||e}var wn;function np(){if(wn===void 0&&(wn=null,kn.trustedTypes))try{wn=kn.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return wn}function Zs(e){return np()?.createScriptURL(e)||e}var ge=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Ea})`}},No=class extends ge{getTypeName(){return"HTML"}},So=class extends ge{getTypeName(){return"Style"}},ko=class extends ge{getTypeName(){return"Script"}},Ro=class extends ge{getTypeName(){return"URL"}},Oo=class extends ge{getTypeName(){return"ResourceURL"}};function Ni(e){return e instanceof ge?e.changingThisBreaksApplicationSecurity:e}function Qc(e,t){let n=rp(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Ea})`)}return n===t}function rp(e){return e instanceof ge&&e.getTypeName()||null}function yb(e){return new No(e)}function vb(e){return new So(e)}function Ib(e){return new ko(e)}function Eb(e){return new Ro(e)}function Db(e){return new Oo(e)}function op(e){let t=new Po(e);return ip()?new Ao(t):t}var Ao=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(fr(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},Po=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=fr(t),n}};function ip(){try{return!!new window.DOMParser().parseFromString(fr(""),"text/html")}catch{return!1}}var sp=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Zc(e){return e=String(e),e.match(sp)?e:"unsafe:"+e}function ve(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function $t(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Yc=ve("area,br,col,hr,img,wbr"),Jc=ve("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Kc=ve("rp,rt"),ap=$t(Kc,Jc),cp=$t(Jc,ve("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),lp=$t(Kc,ve("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Ys=$t(Yc,cp,lp,ap),Xc=ve("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),up=ve("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),dp=ve("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),fp=$t(Xc,up,dp),pp=ve("script,style,template"),Lo=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=mp(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=gp(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Js(t).toLowerCase();if(!Ys.hasOwnProperty(n))return this.sanitizedSomething=!0,!pp.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!fp.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Xc[a]&&(c=Zc(c)),this.buf.push(" ",s,'="',Ks(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=Js(t).toLowerCase();Ys.hasOwnProperty(n)&&!Yc.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Ks(t))}};function hp(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function gp(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw el(t);return t}function mp(e){let t=e.firstChild;if(t&&hp(e,t))throw el(t);return t}function Js(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function el(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var yp=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,vp=/([^\#-~ |!])/g;function Ks(e){return e.replace(/&/g,"&amp;").replace(yp,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(vp,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var bn;function wb(e,t){let n=null;try{bn=bn||op(e);let r=t?String(t):"";n=bn.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=bn.getInertBodyElement(r)}while(r!==i);let a=new Lo().sanitizeChildren(Xs(n)||n);return fr(a)}finally{if(n){let r=Xs(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function Xs(e){return"content"in e&&Ip(e)?e.content:null}function Ip(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Si=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Si||{});function Ep(e){let t=tl();return t?t.sanitize(Si.URL,e)||"":Qc(e,"URL")?Ni(e):Zc(Xe(e))}function Dp(e){let t=tl();if(t)return Zs(t.sanitize(Si.RESOURCE_URL,e)||"");if(Qc(e,"ResourceURL"))return Zs(Ni(e));throw new b(904,!1)}function wp(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?Dp:Ep}function bb(e,t,n){return wp(t,n)(e)}function tl(){let e=_();return e&&e[pe].sanitizer}function Mb(e){return e.ownerDocument.defaultView}function Cb(e){return e.ownerDocument}function nl(e){return e instanceof Function?e():e}function bp(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var rl="ng-template";function Mp(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&bp(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(ki(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function ki(e){return e.type===4&&e.value!==rl}function Cp(e,t,n){let r=e.type===4&&!n?rl:e.value;return t===r}function _p(e,t,n){let r=4,o=e.attrs,i=o!==null?Np(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!X(r)&&!X(c))return!1;if(s&&X(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!Cp(e,c,n)||c===""&&t.length===1){if(X(r))return!1;s=!0}}else if(r&8){if(o===null||!Mp(e,o,c,n)){if(X(r))return!1;s=!0}}else{let l=t[++a],u=Tp(c,o,ki(e),n);if(u===-1){if(X(r))return!1;s=!0;continue}if(l!==""){let f;if(u>i?f="":f=o[u+1].toLowerCase(),r&2&&l!==f){if(X(r))return!1;s=!0}}}}return X(r)||s}function X(e){return(e&1)===0}function Tp(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Sp(t,e)}function xp(e,t,n=!1){for(let r=0;r<t.length;r++)if(_p(e,t[r],n))return!0;return!1}function Np(e){for(let t=0;t<e.length;t++){let n=e[t];if(yc(n))return t}return e.length}function Sp(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function ea(e,t){return e?":not("+t.trim()+")":t}function kp(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!X(s)&&(t+=ea(i,o),o=""),r=s,i=i||!X(r);n++}return o!==""&&(t+=ea(i,o)),t}function Rp(e){return e.map(kp).join(",")}function Op(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!X(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var de={};function Ap(e,t){return e.createText(t)}function Pp(e,t,n){e.setValue(t,n)}function ol(e,t,n){return e.createElement(t,n)}function Zn(e,t,n,r,o){e.insertBefore(t,n,r,o)}function il(e,t,n){e.appendChild(t,n)}function ta(e,t,n,r,o){r!==null?Zn(e,t,n,r,o):il(e,t,n)}function Lp(e,t,n){e.removeChild(null,t,n)}function Fp(e,t,n){e.setAttribute(t,"style",n)}function jp(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function sl(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&mf(e,t,r),o!==null&&jp(e,t,o),i!==null&&Fp(e,t,i)}function Ri(e,t,n,r,o,i,s,a,c,l,u){let f=se+r,p=f+o,d=Vp(f,p),h=typeof l=="function"?l():l;return d[I]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:u}}function Vp(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:de);return n}function Hp(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Ri(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Oi(e,t,n,r,o,i,s,a,c,l,u){let f=t.blueprint.slice();return f[me]=o,f[m]=r|4|128|8|64|1024,(l!==null||e&&e[m]&2048)&&(f[m]|=2048),Xa(f),f[F]=f[at]=e,f[U]=n,f[pe]=s||e&&e[pe],f[A]=a||e&&e[A],f[rt]=c||e&&e[rt]||null,f[te]=i,f[sr]=Uf(),f[jn]=u,f[Ua]=l,f[ie]=t.type==2?e[ie]:f,f}function Bp(e,t,n){let r=ue(t,e),o=Hp(n),i=e[pe].rendererFactory,s=Ai(e,Oi(e,o,null,al(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function al(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function cl(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Ai(e,t){return e[Tt]?e[As][ee]=t:e[Tt]=t,e[As]=t,t}function _b(e=1){ll(H(),_(),Ue()+e,!1)}function ll(e,t,n,r){if(!r)if((t[m]&3)===3){let i=e.preOrderCheckHooks;i!==null&&_n(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Tn(t,i,0,n)}Ve(n)}var pr=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(pr||{});function Fo(e,t,n,r){let o=E(null);try{let[i,s,a]=e.inputs[n],c=null;(s&pr.SignalBased)!==0&&(c=t[i][G]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):za(t,c,i,r)}finally{E(o)}}function ul(e,t,n,r,o){let i=Ue(),s=r&2;try{Ve(-1),s&&t.length>se&&ll(e,t,se,!1),N(s?2:0,o),n(r,o)}finally{Ve(i),N(s?3:1,o)}}function Pi(e,t,n){Gp(e,t,n),(n.flags&64)===64&&Qp(e,t,n)}function dl(e,t,n=ue){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function $p(e,t,n,r){let i=r.get(Yf,Bc)||n===Rt.ShadowDom,s=e.selectRootElement(t,i);return Up(s),s}function Up(e){qp(e)}var qp=()=>null;function Wp(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function fl(e,t,n,r,o,i,s,a){if(!a&&Li(t,e,n,r,o)){ct(t)&&zp(n,t.index);return}if(t.type&3){let c=ue(t,n);r=Wp(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function zp(e,t){let n=le(t,e);n[m]&16||(n[m]|=64)}function Gp(e,t,n){let r=n.directiveStart,o=n.directiveEnd;ct(n)&&Bp(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||zn(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Nt(t,e,s,n);if(Bt(c,t),i!==null&&Kp(t,s-r,c,a,n,i),ae(a)){let l=le(n.index,t);l[U]=Nt(t,e,s,n)}}}function Qp(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=sf();try{Ve(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];yo(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Zp(c,l)}}finally{Ve(-1),yo(s)}}function Zp(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function pl(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];xp(t,i.selectors,!1)&&(r??=[],ae(i)?r.unshift(i):r.push(i))}return r}function Yp(e,t,n,r,o,i){let s=ue(e,t);Jp(t[A],s,i,e.value,n,r,o)}function Jp(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Xe(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function Kp(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];Fo(r,n,c,l)}}function Xp(e,t){let n=e[rt],r=n?n.get(it,null):null;r&&r.handleError(t)}function Li(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],f=t.data[l];Fo(f,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];Fo(u,l,r,o),a=!0}return a}function eh(e,t){let n=le(t,e),r=n[I];th(r,n);let o=n[me];o!==null&&n[jn]===null&&(n[jn]=Wc(o,n[rt])),N(18),Fi(r,n,n[U]),N(19,n[U])}function th(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Fi(e,t,n){Ei(t);try{let r=e.viewQuery;r!==null&&xo(1,r,n);let o=e.template;o!==null&&ul(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[he]?.finishViewCreation(e),e.staticContentQueries&&zc(e,t),e.staticViewQueries&&xo(2,e.viewQuery,n);let i=e.components;i!==null&&nh(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[m]&=-5,Di()}}function nh(e,t){for(let n=0;n<t.length;n++)eh(e,t[n])}function rh(e,t,n,r){let o=E(null);try{let i=t.tView,a=e[m]&4096?4096:16,c=Oi(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[Le]=l;let u=e[he];return u!==null&&(c[he]=u.createEmbeddedView(i)),Fi(i,c,n),c}finally{E(o)}}function na(e,t){return!t||t.firstChild===null||Pc(e)}var oh;function ji(e,t){return oh(e,t)}var jo=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(jo||{});function hl(e){return(e.flags&32)===32}function Je(e,t,n,r,o){if(r!=null){let i,s=!1;ye(r)?i=r:Me(r)&&(s=!0,r=r[me]);let a=ce(r);e===0&&n!==null?o==null?il(t,n,a):Zn(t,n,a,o||null,!0):e===1&&n!==null?Zn(t,n,a,o||null,!0):e===2?Lp(t,a,s):e===3&&t.destroyNode(a),i!=null&&mh(t,e,i,n,o)}}function ih(e,t){gl(e,t),t[me]=null,t[te]=null}function sh(e,t,n,r,o,i){r[me]=o,r[te]=t,hr(e,r,n,1,o,i)}function gl(e,t){t[pe].changeDetectionScheduler?.notify(9),hr(e,t,t[A],2,null,null)}function ah(e){let t=e[Tt];if(!t)return Xr(e[I],e);for(;t;){let n=null;if(Me(t))n=t[Tt];else{let r=t[z];r&&(n=r)}if(!n){for(;t&&!t[ee]&&t!==e;)Me(t)&&Xr(t[I],t),t=t[F];t===null&&(t=e),Me(t)&&Xr(t[I],t),n=t&&t[ee]}t=n}}function Vi(e,t){let n=e[ot],r=n.indexOf(t);n.splice(r,1)}function ml(e,t){if(lt(t))return;let n=t[A];n.destroyNode&&hr(e,t,n,3,null,null),ah(t)}function Xr(e,t){if(lt(t))return;let n=E(null);try{t[m]&=-129,t[m]|=256,t[Y]&&Tr(t[Y]),lh(e,t),ch(e,t),t[I].type===1&&t[A].destroy();let r=t[Le];if(r!==null&&ye(t[F])){r!==t[F]&&Vi(r,t);let o=t[he];o!==null&&o.detachView(e)}_o(t)}finally{E(n)}}function ch(e,t){let n=e.cleanup,r=t[Vn];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Vn]=null);let o=t[be];if(o!==null){t[be]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Hn];if(i!==null){t[Hn]=null;for(let s of i)s.destroy()}}function lh(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof He)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];N(4,a,c);try{c.call(a)}finally{N(5,a,c)}}else{N(4,o,i);try{i.call(o)}finally{N(5,o,i)}}}}}function uh(e,t,n){return dh(e,t.parent,n)}function dh(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[me];if(ct(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Rt.None||o===Rt.Emulated)return null}return ue(r,n)}function fh(e,t,n){return hh(e,t,n)}function ph(e,t,n){return e.type&40?ue(e,n):null}var hh=ph,ra;function Hi(e,t,n,r){let o=uh(e,r,t),i=t[A],s=r.parent||t[te],a=fh(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)ta(i,o,n[c],a,!1);else ta(i,o,n,a,!1);ra!==void 0&&ra(i,r,t,n,o)}function Mt(e,t){if(t!==null){let n=t.type;if(n&3)return ue(t,e);if(n&4)return Vo(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Mt(e,r);{let o=e[t.index];return ye(o)?Vo(-1,o):ce(o)}}else{if(n&128)return Mt(e,t.next);if(n&32)return ji(t,e)()||ce(e[t.index]);{let r=yl(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=je(e[ie]);return Mt(o,r)}else return Mt(e,t.next)}}}return null}function yl(e,t){if(t!==null){let r=e[ie][te],o=t.projection;return r.projection[o]}return null}function Vo(e,t){let n=z+e+1;if(n<t.length){let r=t[n],o=r[I].firstChild;if(o!==null)return Mt(r,o)}return t[Fe]}function Bi(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Bt(ce(a),r),n.flags|=2),!hl(n))if(c&8)Bi(e,t,n.child,r,o,i,!1),Je(t,e,o,a,i);else if(c&32){let l=ji(n,r),u;for(;u=l();)Je(t,e,o,u,i);Je(t,e,o,a,i)}else c&16?gh(e,t,r,n,o,i):Je(t,e,o,a,i);n=s?n.projectionNext:n.next}}function hr(e,t,n,r,o,i){Bi(n,r,e.firstChild,t,o,i,!1)}function gh(e,t,n,r,o,i){let s=n[ie],c=s[te].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];Je(t,e,o,u,i)}else{let l=c,u=s[F];Pc(r)&&(l.flags|=128),Bi(e,t,l,u,o,i,!0)}}function mh(e,t,n,r,o){let i=n[Fe],s=ce(n);i!==s&&Je(t,e,r,i,o);for(let a=z;a<n.length;a++){let c=n[a];hr(c[I],c,e,t,r,i)}}function yh(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:jo.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=jo.Important),e.setStyle(n,r,o,i))}}function Yn(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(ce(i)),ye(i)&&vh(i,r);let s=n.type;if(s&8)Yn(e,t,n.child,r);else if(s&32){let a=ji(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=yl(t,n);if(Array.isArray(a))r.push(...a);else{let c=je(t[ie]);Yn(c[I],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function vh(e,t){for(let n=z;n<e.length;n++){let r=e[n],o=r[I].firstChild;o!==null&&Yn(r[I],r,o,t)}e[Fe]!==e[me]&&t.push(e[Fe])}function vl(e){if(e[Yr]!==null){for(let t of e[Yr])t.impl.addSequence(t);e[Yr].length=0}}var Il=[];function Ih(e){return e[Y]??Eh(e)}function Eh(e){let t=Il.pop()??Object.create(wh);return t.lView=e,t}function Dh(e){e.lView[Y]!==e&&(e.lView=null,Il.push(e))}var wh=re(ne({},ft),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{cr(e.lView)},consumerOnSignalRead(){this.lView[Y]=this}});function bh(e){let t=e[Y]??Object.create(Mh);return t.lView=e,t}var Mh=re(ne({},ft),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=je(e.lView);for(;t&&!El(t[I]);)t=je(t);t&&ec(t)},consumerOnSignalRead(){this.lView[Y]=this}});function El(e){return e.type!==2}function Dl(e){if(e[Hn]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Hn])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[m]&8192)}}var Ch=100;function wl(e,t=!0,n=0){let o=e[pe].rendererFactory,i=!1;i||o.begin?.();try{_h(e,n)}catch(s){throw t&&Xp(e,s),s}finally{i||o.end?.()}}function _h(e,t){let n=ic();try{Ls(!0),Ho(e,t);let r=0;for(;ar(e);){if(r===Ch)throw new b(103,!1);r++,Ho(e,1)}}finally{Ls(n)}}function Th(e,t,n,r){if(lt(t))return;let o=t[m],i=!1,s=!1;Ei(t);let a=!0,c=null,l=null;i||(El(e)?(l=Ih(t),c=Wt(l)):Dr()===null?(a=!1,l=bh(t),c=Wt(l)):t[Y]&&(Tr(t[Y]),t[Y]=null));try{Xa(t),nf(e.bindingStartIndex),n!==null&&ul(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let d=e.preOrderCheckHooks;d!==null&&_n(t,d,null)}else{let d=e.preOrderHooks;d!==null&&Tn(t,d,0,null),Jr(t,0)}if(s||xh(t),Dl(t),bl(t,0),e.contentQueries!==null&&zc(e,t),!i)if(u){let d=e.contentCheckHooks;d!==null&&_n(t,d)}else{let d=e.contentHooks;d!==null&&Tn(t,d,1),Jr(t,1)}Sh(e,t);let f=e.components;f!==null&&Cl(t,f,0);let p=e.viewQuery;if(p!==null&&xo(2,p,r),!i)if(u){let d=e.viewCheckHooks;d!==null&&_n(t,d)}else{let d=e.viewHooks;d!==null&&Tn(t,d,2),Jr(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Zr]){for(let d of t[Zr])d();t[Zr]=null}i||(vl(t),t[m]&=-73)}catch(u){throw i||cr(t),u}finally{l!==null&&(Cr(l,c),a&&Dh(l)),Di()}}function bl(e,t){for(let n=jc(e);n!==null;n=Vc(n))for(let r=z;r<n.length;r++){let o=n[r];Ml(o,t)}}function xh(e){for(let t=jc(e);t!==null;t=Vc(t)){if(!(t[m]&2))continue;let n=t[ot];for(let r=0;r<n.length;r++){let o=n[r];ec(o)}}}function Nh(e,t,n){N(18);let r=le(t,e);Ml(r,n),N(19,r[U])}function Ml(e,t){mi(e)&&Ho(e,t)}function Ho(e,t){let r=e[I],o=e[m],i=e[Y],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&_r(i)),s||=!1,i&&(i.dirty=!1),e[m]&=-9217,s)Th(r,e,r.template,e[U]);else if(o&8192){Dl(e),bl(e,1);let a=r.components;a!==null&&Cl(e,a,1),vl(e)}}function Cl(e,t,n){for(let r=0;r<t.length;r++)Nh(e,t[r],n)}function Sh(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Ve(~o);else{let i=o,s=n[++r],a=n[++r];of(s,i);let c=t[i];N(24,c),a(2,c),N(25,c)}}}finally{Ve(-1)}}function $i(e,t){let n=ic()?64:1088;for(e[pe].changeDetectionScheduler?.notify(t);e;){e[m]|=n;let r=je(e);if($n(e)&&!r)return e;e=r}return null}function _l(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function kh(e,t,n,r=!0){let o=t[I];if(Rh(o,t,e,n),r){let s=Vo(n,e),a=t[A],c=a.parentNode(e[Fe]);c!==null&&sh(o,e[te],a,t,c,s)}let i=t[jn];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Bo(e,t){if(e.length<=z)return;let n=z+t,r=e[n];if(r){let o=r[Le];o!==null&&o!==e&&Vi(o,r),t>0&&(e[n-1][ee]=r[ee]);let i=Pn(e,z+t);ih(r[I],r);let s=i[he];s!==null&&s.detachView(i[I]),r[F]=null,r[ee]=null,r[m]&=-129}return r}function Rh(e,t,n,r){let o=z+r,i=n.length;r>0&&(n[o-1][ee]=t),r<i-z?(t[ee]=n[o],Oa(n,z+r,t)):(n.push(t),t[ee]=null),t[F]=n;let s=t[Le];s!==null&&n!==s&&Tl(s,t);let a=t[he];a!==null&&a.insertView(e),go(t),t[m]|=128}function Tl(e,t){let n=e[ot],r=t[F];if(Me(r))e[m]|=2;else{let o=r[F][ie];t[ie]!==o&&(e[m]|=2)}n===null?e[ot]=[t]:n.push(t)}var Ot=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[I];return Yn(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[U]}set context(t){this._lView[U]=t}get destroyed(){return lt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[F];if(ye(t)){let n=t[Bn],r=n?n.indexOf(this):-1;r>-1&&(Bo(t,r),Pn(n,r))}this._attachedToViewContainer=!1}ml(this._lView[I],this._lView)}onDestroy(t){tc(this._lView,t)}markForCheck(){$i(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[m]&=-129}reattach(){go(this._lView),this._lView[m]|=128}detectChanges(){this._lView[m]|=1024,wl(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new b(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=$n(this._lView),n=this._lView[Le];n!==null&&!t&&Vi(n,this._lView),gl(this._lView[I],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new b(902,!1);this._appRef=t;let n=$n(this._lView),r=this._lView[Le];r!==null&&!n&&Tl(r,this._lView),go(this._lView)}};var Jn=(()=>{class e{static __NG_ELEMENT_ID__=Ph}return e})(),Oh=Jn,Ah=class extends Oh{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=rh(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Ot(o)}};function Ph(){return Ui(q(),_())}function Ui(e,t){return e.type&4?new Ah(t,e,ut(e,t)):null}function qi(e,t,n,r,o){let i=e.data[t];if(i===null)i=Lh(e,t,n,r,o),rf()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Kd();i.injectorIndex=s===null?-1:s.injectorIndex}return Ht(i,!0),i}function Lh(e,t,n,r,o){let i=rc(),s=oc(),a=s?i:i&&i.parent,c=e.data[t]=jh(e,a,n,t,r,o);return Fh(e,c,i,s),c}function Fh(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function jh(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Zd()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var Nb=new RegExp(`^(\\d+)*(${Zf}|${Qf})*(.*)`);var Vh=()=>null;function oa(e,t){return Vh(e,t)}var Hh=class{},xl=class{},$o=class{resolveComponentFactory(t){throw Error(`No component factory found for ${$(t)}.`)}},gr=class{static NULL=new $o},Kn=class{},Ob=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>Bh()}return e})();function Bh(){let e=_(),t=q(),n=le(t.index,e);return(Me(n)?n:e)[A]}var $h=(()=>{class e{static \u0275prov=V({token:e,providedIn:"root",factory:()=>null})}return e})();var eo={},Uo=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=ir(r);let o=this.injector.get(t,eo,r);return o!==eo||n===eo?o:this.parentInjector.get(t,n,r)}};function ia(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Ts(o,a);else if(i==2){let c=a,l=t[++s];r=Ts(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function Wi(e,t=v.Default){let n=_();if(n===null)return Ce(e,t);let r=q();return Mc(r,n,j(e),t)}function Ab(){let e="invalid";throw new Error(e)}function Nl(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,l=null,u=qh(s);u===null?a=s:[a,c,l]=u,Gh(e,t,n,a,i,c,l)}i!==null&&r!==null&&Uh(n,r,i)}function Uh(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new b(-301,!1);r.push(t[o],i)}}function qh(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&ae(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,Wh(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function Wh(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function zh(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Gh(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let d=r[p];!c&&ae(d)&&(c=!0,zh(e,n,p)),Io(zn(n,t),e,d.type)}Xh(n,e.data.length,a);for(let p=0;p<a;p++){let d=r[p];d.providersResolver&&d.providersResolver(d)}let l=!1,u=!1,f=cl(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let d=r[p];if(n.mergedAttrs=xt(n.mergedAttrs,d.hostAttrs),Zh(e,n,t,f,d),Kh(f,d,o),s!==null&&s.has(d)){let[M,P]=s.get(d);n.directiveToIndex.set(d.type,[f,M+n.directiveStart,P+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let h=d.type.prototype;!l&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),f++}Qh(e,n,i)}function Qh(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))sa(0,t,o,r),sa(1,t,o,r),ca(t,r,!1);else{let i=n.get(o);aa(0,t,i,r),aa(1,t,i,r),ca(t,r,!0)}}}function sa(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Sl(t,i)}}function aa(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Sl(t,s)}}function Sl(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function ca(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||ki(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function Zh(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=et(o.type,!0)),s=new He(i,ae(o),Wi);e.blueprint[r]=s,n[r]=s,Yh(e,t,r,cl(e,n,o.hostVars,de),o)}function Yh(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;Jh(s)!=a&&s.push(a),s.push(n,r,i)}}function Jh(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function Kh(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;ae(t)&&(n[""]=e)}}function Xh(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function kl(e,t,n,r,o,i,s,a){let c=t.consts,l=Un(c,s),u=qi(t,e,2,r,l);return i&&Nl(t,n,u,Un(c,a),o),u.mergedAttrs=xt(u.mergedAttrs,u.attrs),u.attrs!==null&&ia(u,u.attrs,!1),u.mergedAttrs!==null&&ia(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function Rl(e,t){gc(e,t),Wa(t)&&e.queries.elementEnd(t)}var Xn=class extends gr{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=tt(t);return new At(n,this.ngModule)}};function eg(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&pr.SignalBased)!==0};return o&&(i.transform=o),i})}function tg(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function ng(e,t,n){let r=t instanceof _e?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Uo(n,r):n}function rg(e){let t=e.get(Kn,null);if(t===null)throw new b(407,!1);let n=e.get($h,null),r=e.get(kt,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function og(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return ol(t,n,n==="svg"?Ya:n==="math"?Vd:null)}var At=class extends xl{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=eg(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=tg(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Rp(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){N(22);let i=E(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:Op(this.componentDef.selectors[0]),c=Ri(0,null,null,1,0,null,null,null,null,[a],null),l=ng(s,o||this.ngModule,t),u=rg(l),f=u.rendererFactory.createRenderer(null,s),p=r?$p(f,r,s.encapsulation,l):og(s,f),d=Oi(null,c,null,512|al(s),null,null,u,f,l,null,Wc(p,l,!0));d[se]=p,Ei(d);let h=null;try{let M=kl(se,c,d,"#host",()=>[this.componentDef],!0,0);p&&(sl(f,p,M),Bt(p,d)),Pi(c,d,M),Gc(c,M,d),Rl(c,M),n!==void 0&&ig(M,this.ngContentSelectors,n),h=le(M.index,d),d[U]=h[U],Fi(c,d,null)}catch(M){throw h!==null&&_o(h),_o(d),M}finally{N(23),Di()}return new qo(this.componentType,d)}finally{E(i)}}},qo=class extends Hh{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=Ka(n[I],se),this.location=ut(this._tNode,n),this.instance=le(this._tNode.index,n)[U],this.hostView=this.changeDetectorRef=new Ot(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Li(r,o[I],o,t,n);this.previousInputValues.set(t,n);let s=le(r.index,o);$i(s,1)}get injector(){return new Ae(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function ig(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var zi=(()=>{class e{static __NG_ELEMENT_ID__=sg}return e})();function sg(){let e=q();return Al(e,_())}var ag=zi,Ol=class extends ag{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return ut(this._hostTNode,this._hostLView)}get injector(){return new Ae(this._hostTNode,this._hostLView)}get parentInjector(){let t=Ci(this._hostTNode,this._hostLView);if(vc(t)){let n=Wn(t,this._hostLView),r=qn(t),o=n[I].data[r+8];return new Ae(o,n)}else return new Ae(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=la(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-z}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=oa(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,na(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Pd(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let c=s?t:new At(tt(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let M=(s?l:this.parentInjector).get(_e,null);M&&(i=M)}let u=tt(c.componentType??{}),f=oa(this._lContainer,u?.id??null),p=f?.firstChild??null,d=c.create(l,o,p,i);return this.insertImpl(d.hostView,a,na(this._hostTNode,f)),d}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if($d(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[F],l=new Ol(c,c[te],c[F]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return kh(s,o,i,r),t.attachToViewContainerRef(),Oa(to(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=la(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Bo(this._lContainer,n);r&&(Pn(to(this._lContainer),n),ml(r[I],r))}detach(t){let n=this._adjustIndex(t,-1),r=Bo(this._lContainer,n);return r&&Pn(to(this._lContainer),n)!=null?new Ot(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function la(e){return e[Bn]}function to(e){return e[Bn]||(e[Bn]=[])}function Al(e,t){let n,r=t[e.index];return ye(r)?n=r:(n=_l(r,t,null,e),t[e.index]=n,Ai(t,n)),lg(n,t,e,r),new Ol(n,e,t)}function cg(e,t){let n=e[A],r=n.createComment(""),o=ue(t,e),i=n.parentNode(o);return Zn(n,i,r,n.nextSibling(o),!1),r}var lg=fg,ug=()=>!1;function dg(e,t,n){return ug(e,t,n)}function fg(e,t,n,r){if(e[Fe])return;let o;n.type&8?o=ce(r):o=cg(t,n),e[Fe]=o}var Wo=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},zo=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Gi(t,n).matches!==null&&this.queries[n].setDirty()}},Go=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=Eg(t):this.predicate=t}},Qo=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Zo=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,pg(n,i)),this.matchTNodeWithReadOption(t,n,xn(n,t,i,!1,!1))}else r===Jn?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,xn(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===dr||o===zi||o===Jn&&n.type&4)this.addMatch(n.index,-2);else{let i=xn(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function pg(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function hg(e,t){return e.type&11?ut(e,t):e.type&4?Ui(e,t):null}function gg(e,t,n,r){return n===-1?hg(t,e):n===-2?mg(e,t,r):Nt(e,e[I],n,t)}function mg(e,t,n){if(n===dr)return ut(t,e);if(n===Jn)return Ui(t,e);if(n===zi)return Al(t,e)}function Pl(e,t,n,r){let o=t[he].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(gg(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Yo(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Pl(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let f=z;f<u.length;f++){let p=u[f];p[Le]===p[F]&&Yo(p[I],p,l,r)}if(u[ot]!==null){let f=u[ot];for(let p=0;p<f.length;p++){let d=f[p];Yo(d[I],d,l,r)}}}}}return r}function yg(e,t){return e[he].queries[t].queryList}function vg(e,t,n){let r=new Co((n&4)===4);return Wd(e,t,r,r.destroy),(t[he]??=new zo).queries.push(new Wo(r))-1}function Ig(e,t,n,r){let o=H();if(o.firstCreatePass){let i=q();Dg(o,new Go(t,n,r),i.index),wg(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return vg(o,_(),n)}function Eg(e){return e.split(",").map(t=>t.trim())}function Dg(e,t,n){e.queries===null&&(e.queries=new Qo),e.queries.track(new Zo(t,n))}function wg(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function Gi(e,t){return e.queries.getByIndex(t)}function bg(e,t){let n=e[I],r=Gi(n,t);return r.crossesNgTemplate?Yo(n,e,t,[]):Pl(n,e,r,t)}var Pt=class{},Mg=class{};var Jo=class extends Pt{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Xn(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=La(t);this._bootstrapComponents=nl(i.bootstrap),this._r3Injector=Tc(t,n,[{provide:Pt,useValue:this},{provide:gr,useValue:this.componentFactoryResolver},...r],$(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Ko=class extends Mg{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Jo(this.moduleType,t,[])}};var er=class extends Pt{injector;componentFactoryResolver=new Xn(this);instance=null;constructor(t){super();let n=new _t([...t.providers,{provide:Pt,useValue:this},{provide:gr,useValue:this.componentFactoryResolver}],t.parent||hi(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Cg(e,t,n=null){return new er({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var _g=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Fa(!1,n.type),o=r.length>0?Cg([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=V({token:e,providedIn:"environment",factory:()=>new e(Ce(_e))})}return e})();function jb(e){return jt(()=>{let t=Ll(e),n=re(ne({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Lc.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(_g).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Rt.Emulated,styles:e.styles||Z,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&qc("NgStandalone"),Fl(n);let r=e.dependencies;return n.directiveDefs=ua(r,!1),n.pipeDefs=ua(r,!0),n.id=kg(n),n})}function Tg(e){return tt(e)||Dd(e)}function xg(e){return e!==null}function Vb(e){return jt(()=>({type:e.type,bootstrap:e.bootstrap||Z,declarations:e.declarations||Z,imports:e.imports||Z,exports:e.exports||Z,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Ng(e,t){if(e==null)return Pe;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=pr.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function Sg(e){if(e==null)return Pe;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function Hb(e){return jt(()=>{let t=Ll(e);return Fl(t),t})}function Ll(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Pe,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Z,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Ng(e.inputs,t),outputs:Sg(e.outputs),debugInfo:null}}function Fl(e){e.features?.forEach(t=>t(e))}function ua(e,t){if(!e)return null;let n=t?wd:Tg;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(xg)}function kg(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Rg(e){return Object.getPrototypeOf(e.prototype).constructor}function Og(e){let t=Rg(e.type),n=!0,r=[e];for(;t;){let o;if(ae(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new b(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=no(e.inputs),s.declaredInputs=no(e.declaredInputs),s.outputs=no(e.outputs);let a=o.hostBindings;a&&jg(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&Lg(e,c),l&&Fg(e,l),Ag(e,o),Ju(e.outputs,o.outputs),ae(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Og&&(n=!1)}}t=Object.getPrototypeOf(t)}Pg(r)}function Ag(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function Pg(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=xt(o.hostAttrs,n=xt(n,o.hostAttrs))}}function no(e){return e===Pe?{}:e===Z?[]:e}function Lg(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function Fg(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function jg(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function jl(e){return Hg(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Vg(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Hg(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Vl(e,t,n){return e[t]=n}function Bg(e,t){return e[t]}function Be(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Xo(e,t,n,r){let o=Be(e,t,n);return Be(e,t+1,r)||o}function $g(e,t,n,r,o,i){let s=Xo(e,t,n,r);return Xo(e,t+2,o,i)||s}function Ug(e,t,n,r,o,i,s,a,c){let l=t.consts,u=qi(t,e,4,s||null,a||null);nc()&&Nl(t,n,u,Un(l,c),pl),u.mergedAttrs=xt(u.mergedAttrs,u.attrs),gc(t,u);let f=u.tView=Ri(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),f.queries=t.queries.embeddedTView(u)),u}function qg(e,t,n,r,o,i,s,a,c,l){let u=n+se,f=t.firstCreatePass?Ug(u,t,e,r,o,i,s,a,c):t.data[u];Ht(f,!1);let p=zg(t,e,f,n);bi()&&Hi(t,e,p,f),Bt(p,e);let d=_l(p,e,p,f);return e[u]=d,Ai(e,d),dg(d,f,e),gi(f)&&Pi(t,e,f),c!=null&&dl(e,f,l),f}function Wg(e,t,n,r,o,i,s,a){let c=_(),l=H(),u=Un(l.consts,i);return qg(c,l,e,t,n,r,o,u,s,a),Wg}var zg=Gg;function Gg(e,t,n,r){return Mi(!0),t[A].createComment("")}var Bb=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Qg=new S("");var Zg=(()=>{class e{static \u0275prov=V({token:e,providedIn:"root",factory:()=>new ei})}return e})(),ei=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function Hl(e){return!!e&&typeof e.then=="function"}function Yg(e){return!!e&&typeof e.subscribe=="function"}var Jg=new S("");var Bl=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=T(Jg,{optional:!0})??[];injector=T(St);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=$a(this.injector,o);if(Hl(i))n.push(i);else if(Yg(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Kg=new S("");function Xg(){Nr(()=>{throw new b(600,!1)})}function em(e){return e.isBoundToModule}var tm=10;var Lt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=T(Pf);afterRenderManager=T(Jf);zonelessEnabled=T(_i);rootEffectScheduler=T(Zg);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new Ie;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=T(ur).hasPendingTasks.pipe(ke(n=>!n));constructor(){T(Uc,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=T(_e);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=St.NULL){N(10);let i=n instanceof xl;if(!this._injector.get(Bl).done){let d="";throw new b(405,d)}let a;i?a=n:a=this._injector.get(gr).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=em(a)?void 0:this._injector.get(Pt),l=r||a.selector,u=a.create(o,[],l,c),f=u.location.nativeElement,p=u.injector.get(Qg,null);return p?.registerApplication(f),u.onDestroy(()=>{this.detachView(u.hostView),Nn(this.components,u),p?.unregisterApplication(f)}),this._loadComponent(u),N(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){N(12),this.tracingSnapshot!==null?this.tracingSnapshot.run($c.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new b(101,!1);let n=E(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,E(n),this.afterTick.next(),N(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Kn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<tm;)N(14),this.synchronizeOnce(),N(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)nm(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ar(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Nn(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Kg,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Nn(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new b(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Nn(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function nm(e,t,n,r){if(!n&&!ar(e))return;wl(e,t,n&&!r?0:1)}function rm(e,t,n,r){let o=_(),i=lr();if(Be(o,i,t)){let s=H(),a=wi();Yp(a,o,e,t,n,r)}return rm}function om(e,t,n,r){return Be(e,lr(),n)?t+Xe(n)+r:de}function im(e,t,n,r,o,i){let s=tf(),a=Xo(e,s,n,o);return ac(2),a?t+Xe(n)+r+Xe(o)+i:de}function Mn(e,t){return e<<17|t<<2}function $e(e){return e>>17&32767}function sm(e){return(e&2)==2}function am(e,t){return e&131071|t<<17}function ti(e){return e|2}function st(e){return(e&131068)>>2}function ro(e,t){return e&-131069|t<<2}function cm(e){return(e&1)===1}function ni(e){return e|1}function lm(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=$e(s),c=st(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let f=n;u=f[1],(u===null||Vt(f,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let p=$e(e[a+1]);e[r+1]=Mn(p,a),p!==0&&(e[p+1]=ro(e[p+1],r)),e[a+1]=am(e[a+1],r)}else e[r+1]=Mn(a,0),a!==0&&(e[a+1]=ro(e[a+1],r)),a=r;else e[r+1]=Mn(c,0),a===0?a=r:e[c+1]=ro(e[c+1],r),c=r;l&&(e[r+1]=ti(e[r+1])),da(e,u,r,!0),da(e,u,r,!1),um(t,u,e,r,i),s=Mn(a,c),i?t.classBindings=s:t.styleBindings=s}function um(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Vt(i,t)>=0&&(n[r+1]=ni(n[r+1]))}function da(e,t,n,r){let o=e[n+1],i=t===null,s=r?$e(o):st(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];dm(c,t)&&(a=!0,e[s+1]=r?ni(l):ti(l)),s=r?$e(l):st(l)}a&&(e[n+1]=r?ti(o):ni(o))}function dm(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Vt(e,t)>=0:!1}function fm(e,t,n){let r=_(),o=lr();if(Be(r,o,t)){let i=H(),s=wi();fl(i,s,r,e,t,r[A],n,!1)}return fm}function fa(e,t,n,r,o){Li(t,e,n,o?"class":"style",r)}function pm(e,t){return hm(e,t,null,!0),pm}function hm(e,t,n,r){let o=_(),i=H(),s=ac(2);if(i.firstUpdatePass&&mm(i,e,s,r),t!==de&&Be(o,s,t)){let a=i.data[Ue()];Dm(i,a,o,o[A],e,o[s+1]=wm(t,n),r,s)}}function gm(e,t){return t>=e.expandoStartIndex}function mm(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Ue()],s=gm(e,n);bm(i,r)&&t===null&&!s&&(t=!1),t=ym(o,i,t,r),lm(o,i,t,n,s,r)}}function ym(e,t,n,r){let o=af(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=oo(null,e,t,n,r),n=Ft(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=oo(o,e,t,n,r),i===null){let c=vm(e,t,r);c!==void 0&&Array.isArray(c)&&(c=oo(null,e,t,c[1],r),c=Ft(c,t.attrs,r),Im(e,t,r,c))}else i=Em(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function vm(e,t,n){let r=n?t.classBindings:t.styleBindings;if(st(r)!==0)return e[$e(r)]}function Im(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[$e(o)]=r}function Em(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Ft(r,s,n)}return Ft(r,t.attrs,n)}function oo(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Ft(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Ft(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Id(e,s,n?!0:t[++i]))}return e===void 0?null:e}function Dm(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=cm(l)?pa(c,t,n,o,st(l),s):void 0;if(!tr(u)){tr(i)||sm(l)&&(i=pa(c,null,n,o,a,s));let f=Ja(Ue(),n);yh(r,s,f,o,i)}}function pa(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,f=u===null,p=n[o+1];p===de&&(p=f?Z:void 0);let d=f?Gr(p,r):u===r?p:void 0;if(l&&!tr(d)&&(d=Gr(c,r)),tr(d)&&(a=d,s))return a;let h=e[o+1];o=s?$e(h):st(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Gr(c,r))}return a}function tr(e){return e!==void 0}function wm(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=$(Ni(e)))),e}function bm(e,t){return(e.flags&(t?8:16))!==0}function $l(e,t,n,r){let o=_(),i=H(),s=se+e,a=o[A],c=i.firstCreatePass?kl(s,i,o,t,pl,nc(),n,r):i.data[s],l=Cm(i,o,c,a,t,e);o[s]=l;let u=gi(c);return Ht(c,!0),sl(a,l,c),!hl(c)&&bi()&&Hi(i,o,l,c),(zd()===0||u)&&Bt(l,o),Gd(),u&&(Pi(i,o,c),Gc(i,c,o)),r!==null&&dl(o,c),$l}function Ul(){let e=q();oc()?Xd():(e=e.parent,Ht(e,!1));let t=e;Yd(t)&&Jd(),Qd();let n=H();return n.firstCreatePass&&Rl(n,t),t.classesWithoutHost!=null&&hf(t)&&fa(n,t,_(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&gf(t)&&fa(n,t,_(),t.stylesWithoutHost,!1),Ul}function Mm(e,t,n,r){return $l(e,t,n,r),Ul(),Mm}var Cm=(e,t,n,r,o,i)=>(Mi(!0),ol(r,o,df()));function $b(){return _()}var nr="en-US";var _m=nr;function Tm(e){typeof e=="string"&&(_m=e.toLowerCase().replace(/_/g,"-"))}function ha(e,t,n){return function r(o){if(o===Function)return n;let i=ct(e)?le(e.index,t):t;$i(i,5);let s=t[U],a=ga(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=ga(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function ga(e,t,n,r){let o=E(null);try{return N(6,t,n),n(r)!==!1}catch(i){return xm(e,i),!1}finally{N(7,t,n),E(o)}}function xm(e,t){let n=e[rt],r=n?n.get(it,null):null;r&&r.handleError(t)}function ma(e,t,n,r,o,i){let s=t[n],a=t[I],l=a.data[n].outputs[r],u=s[l],f=a.firstCreatePass?vi(a):null,p=yi(t),d=u.subscribe(i),h=p.length;p.push(i,d),f&&f.push(o,e.index,h,-(h+1))}function Nm(e,t,n,r){let o=_(),i=H(),s=q();return ql(i,o,o[A],s,e,t,r),Nm}function Sm(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Vn],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function ql(e,t,n,r,o,i,s){let a=gi(r),l=e.firstCreatePass?vi(e):null,u=yi(t),f=!0;if(r.type&3||s){let p=ue(r,t),d=s?s(p):p,h=u.length,M=s?k=>s(ce(k[r.index])):r.index,P=null;if(!s&&a&&(P=Sm(e,t,o,r.index)),P!==null){let k=P.__ngLastListenerFn__||P;k.__ngNextListenerFn__=i,P.__ngLastListenerFn__=i,f=!1}else{i=ha(r,t,i),Xf(t,d,o,i);let k=n.listen(d,o,i);u.push(i,k),l&&l.push(o,M,h,h+1)}}else i=ha(r,t,i);if(f){let p=r.outputs?.[o],d=r.hostDirectiveOutputs?.[o];if(d&&d.length)for(let h=0;h<d.length;h+=2){let M=d[h],P=d[h+1];ma(r,t,M,P,o,i)}if(p&&p.length)for(let h of p)ma(r,t,h,o,o,i)}}function Ub(e=1){return lf(e)}function qb(e,t,n,r){Ig(e,t,n,r)}function Wb(e){let t=_(),n=H(),r=cc();Ii(r+1);let o=Gi(n,r);if(e.dirty&&Bd(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=bg(t,r);e.reset(i,jf),e.notifyOnChanges()}return!0}return!1}function zb(){return yg(_(),cc())}function Gb(e){let t=ef();return Hd(t,se+e)}function Qb(e,t=""){let n=_(),r=H(),o=e+se,i=r.firstCreatePass?qi(r,o,1,t,null):r.data[o],s=km(r,n,i,t,e);n[o]=s,bi()&&Hi(r,n,s,i),Ht(i,!1)}var km=(e,t,n,r,o)=>(Mi(!0),Ap(t[A],r));function Rm(e){return Wl("",e,""),Rm}function Wl(e,t,n){let r=_(),o=om(r,e,t,n);return o!==de&&zl(r,Ue(),o),Wl}function Om(e,t,n,r,o){let i=_(),s=im(i,e,t,n,r,o);return s!==de&&zl(i,Ue(),s),Om}function zl(e,t,n){let r=Ja(t,e);Pp(e[A],r,n)}function Am(e,t,n){Ac(t)&&(t=t());let r=_(),o=lr();if(Be(r,o,t)){let i=H(),s=wi();fl(i,s,r,e,t,r[A],n,!1)}return Am}function Zb(e,t){let n=Ac(e);return n&&e.set(t),n}function Pm(e,t){let n=_(),r=H(),o=q();return ql(r,n,n[A],o,e,t),Pm}function Lm(e,t,n){let r=H();if(r.firstCreatePass){let o=ae(e);ri(n,r.data,r.blueprint,o,!0),ri(t,r.data,r.blueprint,o,!1)}}function ri(e,t,n,r,o){if(e=j(e),Array.isArray(e))for(let i=0;i<e.length;i++)ri(e[i],t,n,r,o);else{let i=H(),s=_(),a=q(),c=nt(e)?e:j(e.provide),l=Ba(e),u=a.providerIndexes&1048575,f=a.directiveStart,p=a.providerIndexes>>20;if(nt(e)||!e.multi){let d=new He(l,o,Wi),h=so(c,t,o?u:u+p,f);h===-1?(Io(zn(a,s),i,c),io(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(d),s.push(d)):(n[h]=d,s[h]=d)}else{let d=so(c,t,u+p,f),h=so(c,t,u,u+p),M=d>=0&&n[d],P=h>=0&&n[h];if(o&&!P||!o&&!M){Io(zn(a,s),i,c);let k=Vm(o?jm:Fm,n.length,o,r,l);!o&&P&&(n[h].providerFactory=k),io(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(k),s.push(k)}else{let k=Gl(n[o?h:d],l,!o&&r);io(i,e,d>-1?d:h,k)}!o&&r&&P&&n[h].componentProviders++}}}function io(e,t,n,r){let o=nt(t),i=xd(t);if(o||i){let c=(i?j(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function Gl(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function so(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function Fm(e,t,n,r,o){return oi(this.multi,[])}function jm(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=Nt(r,r[I],this.providerFactory.index,o);s=c.slice(0,a),oi(i,s);for(let l=a;l<c.length;l++)s.push(c[l])}else s=[],oi(i,s);return s}function oi(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function Vm(e,t,n,r,o){let i=new He(e,n,Wi);return i.multi=[],i.index=t,i.componentProviders=0,Gl(i,o,r&&!n),i}function Yb(e,t=[]){return n=>{n.providersResolver=(r,o)=>Lm(r,o?o(e):e,t)}}function Jb(e,t,n){let r=sc()+e,o=_();return o[r]===de?Vl(o,r,n?t.call(n):t()):Bg(o,r)}function Kb(e,t,n,r,o,i,s){return Bm(_(),sc(),e,t,n,r,o,i,s)}function Hm(e,t){let n=e[t];return n===de?void 0:n}function Bm(e,t,n,r,o,i,s,a,c){let l=t+n;return $g(e,l,o,i,s,a)?Vl(e,l+4,c?r.call(c,o,i,s,a):r(o,i,s,a)):Hm(e,l+4)}var ii=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},Xb=(()=>{class e{compileModuleSync(n){return new Ko(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=La(n),i=nl(o.declarations).reduce((s,a)=>{let c=tt(a);return c&&s.push(new At(c)),s},[]);return new ii(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var $m=(()=>{class e{zone=T(J);changeDetectionScheduler=T(kt);applicationRef=T(Lt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Um=new S("",{factory:()=>!1});function Ql({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new J(re(ne({},Zl()),{scheduleInRootZone:n})),[{provide:J,useFactory:e},{provide:Ln,multi:!0,useFactory:()=>{let r=T($m,{optional:!0});return()=>r.initialize()}},{provide:Ln,multi:!0,useFactory:()=>{let r=T(qm);return()=>{r.initialize()}}},t===!0?{provide:Sc,useValue:!0}:[],{provide:kc,useValue:n??xc}]}function eM(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=Ql({ngZoneFactory:()=>{let o=Zl(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&qc("NgZone_CoalesceEvent"),new J(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return bd([{provide:Um,useValue:!0},{provide:_i,useValue:!1},r])}function Zl(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var qm=(()=>{class e{subscription=new L;initialized=!1;zone=T(J);pendingTasks=T(ur);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{J.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{J.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Wm=(()=>{class e{appRef=T(Lt);taskService=T(ur);ngZone=T(J);zonelessEnabled=T(_i);tracing=T(Uc,{optional:!0});disableScheduling=T(Sc,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new L;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Qn):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(T(kc,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Mo||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Us:Rc;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Qn+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Us(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function zm(){return typeof $localize<"u"&&$localize.locale||nr}var Yl=new S("",{providedIn:"root",factory:()=>T(Yl,v.Optional|v.SkipSelf)||zm()});var si=new S(""),Gm=new S("");function wt(e){return!e.moduleRef}function Qm(e){let t=wt(e)?e.r3Injector:e.moduleRef.injector,n=t.get(J);return n.run(()=>{wt(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(it,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),wt(e)){let i=()=>t.destroy(),s=e.platformInjector.get(si);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(si);s.add(i),e.moduleRef.onDestroy(()=>{Nn(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return Ym(r,n,()=>{let i=t.get(Bl);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(Yl,nr);if(Tm(s||nr),!t.get(Gm,!0))return wt(e)?t.get(Lt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(wt(e)){let c=t.get(Lt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return Zm(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function Zm(e,t){let n=e.injector.get(Lt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new b(-403,!1);t.push(e)}function Ym(e,t,n){try{let r=n();return Hl(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var Sn=null;function Jm(e=[],t){return St.create({name:t,providers:[{provide:Ha,useValue:"platform"},{provide:si,useValue:new Set([()=>Sn=null])},...e]})}function Km(e=[]){if(Sn)return Sn;let t=Jm(e);return Sn=t,Xg(),Xm(t),t}function Xm(e){let t=e.get(Gf,null);$a(e,()=>{t?.forEach(n=>n())})}var tM=(()=>{class e{static __NG_ELEMENT_ID__=ey}return e})();function ey(e){return ty(q(),_(),(e&16)===16)}function ty(e,t,n){if(ct(e)&&!n){let r=le(e.index,t);return new Ot(r,r)}else if(e.type&175){let r=t[ie];return new Ot(r,t)}return null}var ai=class{constructor(){}supports(t){return jl(t)}create(t){return new ci(t)}},ny=(e,t)=>t,ci=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||ny}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<ya(r,o,i)?n:r,a=ya(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let p=0;p<l;p++){let d=p<i.length?i[p]:i[p]=0,h=d+p;u<=h&&h<l&&(i[p]=d+1)}let f=s.previousIndex;i[f]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!jl(t))throw new b(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,Vg(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new li(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new rr),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new rr),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},li=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},ui=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},rr=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new ui,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function ya(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function va(){return new ry([new ai])}var ry=(()=>{class e{factories;static \u0275prov=V({token:e,providedIn:"root",factory:va});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||va()),deps:[[e,new gd,new hd]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new b(901,!1)}}return e})();function nM(e){N(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=Km(r),i=[Ql({}),{provide:kt,useExisting:Wm},...n||[]],s=new er({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return Qm({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{N(9)}}function rM(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function oM(e){return Rr(e)}function iM(e,t){return xr(e,t?.equal)}var Ia=class{[G];constructor(t){this[G]=t}destroy(){this[G].destroy()}};export{ne as a,re as b,L as c,pu as d,$r as e,Ur as f,Ie as g,vt as h,It as i,De as j,bu as k,Mu as l,Cu as m,Se as n,ke as o,Ou as p,Re as q,qr as r,vn as s,Pu as t,Lu as u,Et as v,Cs as w,Fu as x,Dt as y,Wr as z,Vu as A,Hu as B,zr as C,Bu as D,$u as E,Uu as F,qu as G,Wu as H,zu as I,b as J,Ma as K,V as L,nb as M,rb as N,S as O,v as P,Ce as Q,T as R,bd as S,Ha as T,_e as U,$a as V,ob as W,ib as X,sb as Y,ab as Z,cb as _,lb as $,ub as aa,St as ba,Nc as ca,ur as da,we as ea,J as fa,it as ga,db as ha,dr as ia,fb as ja,pb as ka,hb as la,Gf as ma,gb as na,mb as oa,Uc as pa,Rt as qa,Ni as ra,Qc as sa,yb as ta,vb as ua,Ib as va,Eb as wa,Db as xa,Zc as ya,wb as za,Si as Aa,Dp as Ba,bb as Ca,Mb as Da,Cb as Ea,_b as Fa,jo as Ga,Jn as Ha,Kn as Ia,Ob as Ja,Wi as Ka,Ab as La,zi as Ma,Mg as Na,Cg as Oa,jb as Pa,Vb as Qa,Hb as Ra,Og as Sa,Wg as Ta,Bb as Ua,Hl as Va,Kg as Wa,Lt as Xa,rm as Ya,fm as Za,pm as _a,$l as $a,Ul as ab,Mm as bb,$b as cb,Nm as db,Ub as eb,qb as fb,Wb as gb,zb as hb,Gb as ib,Qb as jb,Rm as kb,Wl as lb,Om as mb,Am as nb,Zb as ob,Pm as pb,Yb as qb,Jb as rb,Kb as sb,Xb as tb,eM as ub,tM as vb,ry as wb,nM as xb,rM as yb,oM as zb,iM as Ab};
