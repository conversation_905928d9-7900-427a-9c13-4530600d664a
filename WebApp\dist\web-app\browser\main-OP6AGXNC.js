import{a as pr,b as fr}from"./chunk-5PTNFOH4.js";import{e as Kt,f as Ne,i as ur,j as dr,o as hr}from"./chunk-XP6X4NQJ.js";import{$ as Wt,$a as u,A as nt,B as J,C as Vt,Ca as Hn,D as Pn,Da as Wn,E as _n,F as Un,G as z,H as kn,I as b,J as x,Ja as Yt,Ka as T,L as w,La as Gn,Ma as Qn,N as Nn,Na as Yn,O as N,Oa as Zt,P as Ht,Pa as F,Q as fe,R as f,Ra as Ue,S as jn,Ta as Zn,U as Pe,Ua as Kn,V as Z,Va as Xn,W as _e,Wa as Jn,X as Ln,Xa as er,Y as $n,Ya as tr,Z as se,Za as nr,_ as rt,a as d,aa as zn,ab as h,b as U,ba as Fn,bb as j,c as An,ca as Bn,cb as rr,d as Mn,da as qn,db as ke,e as Lt,ea as ae,eb as ir,f as $t,fa as Gt,fb as or,g as G,gb as sr,h as k,ha as Vn,hb as ar,i as Q,ia as Qt,j as E,jb as m,k as p,l as De,m as On,n as En,o as C,p as zt,q as D,r as Ft,s as Dn,t as Bt,tb as lr,ub as cr,v as Y,vb as it,w as oe,x as he,y as qt,yb as ot,z as pe}from"./chunk-RZLO7X7E.js";var g="primary",Ye=Symbol("RouteTitle"),nn=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e[0]:e}return null}getAll(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function ue(t){return new nn(t)}function Rr(t,n,e){let r=e.path.split("/");if(r.length>t.length||e.pathMatch==="full"&&(n.hasChildren()||r.length<t.length))return null;let i={};for(let s=0;s<r.length;s++){let o=r[s],a=t[s];if(o[0]===":")i[o.substring(1)]=a;else if(o!==a.path)return null}return{consumed:t.slice(0,r.length),posParams:i}}function ci(t,n){if(t.length!==n.length)return!1;for(let e=0;e<t.length;++e)if(!B(t[e],n[e]))return!1;return!0}function B(t,n){let e=t?rn(t):void 0,r=n?rn(n):void 0;if(!e||!r||e.length!=r.length)return!1;let i;for(let s=0;s<e.length;s++)if(i=e[s],!br(t[i],n[i]))return!1;return!0}function rn(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function br(t,n){if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return!1;let e=[...t].sort(),r=[...n].sort();return e.every((i,s)=>r[s]===i)}else return t===n}function xr(t){return t.length>0?t[t.length-1]:null}function re(t){return On(t)?t:Xn(t)?E(Promise.resolve(t)):p(t)}var ui={exact:Tr,subset:Ar},Ir={exact:di,subset:hi,ignored:()=>!0};function gr(t,n,e){return ui[e.paths](t.root,n.root,e.matrixParams)&&Ir[e.queryParams](t.queryParams,n.queryParams)&&!(e.fragment==="exact"&&t.fragment!==n.fragment)}function di(t,n){return B(t,n)}function Tr(t,n,e){if(!le(t.segments,n.segments)||!lt(t.segments,n.segments,e)||t.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!t.children[r]||!Tr(t.children[r],n.children[r],e))return!1;return!0}function hi(t,n){return Object.keys(n).length<=Object.keys(t).length&&Object.keys(n).every(e=>br(t[e],n[e]))}function Ar(t,n,e){return Mr(t,n,n.segments,e)}function Mr(t,n,e,r){if(t.segments.length>e.length){let i=t.segments.slice(0,e.length);return!(!le(i,e)||n.hasChildren()||!lt(i,e,r))}else if(t.segments.length===e.length){if(!le(t.segments,e)||!lt(t.segments,e,r))return!1;for(let i in n.children)if(!t.children[i]||!Ar(t.children[i],n.children[i],r))return!1;return!0}else{let i=e.slice(0,t.segments.length),s=e.slice(t.segments.length);return!le(t.segments,i)||!lt(t.segments,i,r)||!t.children[g]?!1:Mr(t.children[g],n,s,r)}}function lt(t,n,e){return n.every((r,i)=>Ir[e](t[i].parameters,r.parameters))}var V=class{root;queryParams;fragment;_queryParamMap;constructor(n=new S([],{}),e={},r=null){this.root=n,this.queryParams=e,this.fragment=r}get queryParamMap(){return this._queryParamMap??=ue(this.queryParams),this._queryParamMap}toString(){return gi.serialize(this)}},S=class{segments;children;parent=null;constructor(n,e){this.segments=n,this.children=e,Object.values(e).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return ct(this)}},ee=class{path;parameters;_parameterMap;constructor(n,e){this.path=n,this.parameters=e}get parameterMap(){return this._parameterMap??=ue(this.parameters),this._parameterMap}toString(){return Er(this)}};function pi(t,n){return le(t,n)&&t.every((e,r)=>B(e.parameters,n[r].parameters))}function le(t,n){return t.length!==n.length?!1:t.every((e,r)=>e.path===n[r].path)}function fi(t,n){let e=[];return Object.entries(t.children).forEach(([r,i])=>{r===g&&(e=e.concat(n(i,r)))}),Object.entries(t.children).forEach(([r,i])=>{r!==g&&(e=e.concat(n(i,r)))}),e}var Te=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=w({token:t,factory:()=>new de,providedIn:"root"})}return t})(),de=class{parse(n){let e=new sn(n);return new V(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(n){let e=`/${je(n.root,!0)}`,r=Si(n.queryParams),i=typeof n.fragment=="string"?`#${mi(n.fragment)}`:"";return`${e}${r}${i}`}},gi=new de;function ct(t){return t.segments.map(n=>Er(n)).join("/")}function je(t,n){if(!t.hasChildren())return ct(t);if(n){let e=t.children[g]?je(t.children[g],!1):"",r=[];return Object.entries(t.children).forEach(([i,s])=>{i!==g&&r.push(`${i}:${je(s,!1)}`)}),r.length>0?`${e}(${r.join("//")})`:e}else{let e=fi(t,(r,i)=>i===g?[je(t.children[g],!1)]:[`${i}:${je(r,!1)}`]);return Object.keys(t.children).length===1&&t.children[g]!=null?`${ct(t)}/${e[0]}`:`${ct(t)}/(${e.join("//")})`}}function Or(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function st(t){return Or(t).replace(/%3B/gi,";")}function mi(t){return encodeURI(t)}function on(t){return Or(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function ut(t){return decodeURIComponent(t)}function mr(t){return ut(t.replace(/\+/g,"%20"))}function Er(t){return`${on(t.path)}${vi(t.parameters)}`}function vi(t){return Object.entries(t).map(([n,e])=>`;${on(n)}=${on(e)}`).join("")}function Si(t){let n=Object.entries(t).map(([e,r])=>Array.isArray(r)?r.map(i=>`${st(e)}=${st(i)}`).join("&"):`${st(e)}=${st(r)}`).filter(e=>e);return n.length?`?${n.join("&")}`:""}var Ci=/^[^\/()?;#]+/;function Xt(t){let n=t.match(Ci);return n?n[0]:""}var yi=/^[^\/()?;=#]+/;function wi(t){let n=t.match(yi);return n?n[0]:""}var Ri=/^[^=?&#]+/;function bi(t){let n=t.match(Ri);return n?n[0]:""}var xi=/^[^&#]+/;function Ii(t){let n=t.match(xi);return n?n[0]:""}var sn=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new S([],{}):new S([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(e).length>0)&&(r[g]=new S(n,e)),r}parseSegment(){let n=Xt(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new x(4009,!1);return this.capture(n),new ee(ut(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let e=wi(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let i=Xt(this.remaining);i&&(r=i,this.capture(r))}n[ut(e)]=ut(r)}parseQueryParam(n){let e=bi(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let o=Ii(this.remaining);o&&(r=o,this.capture(r))}let i=mr(e),s=mr(r);if(n.hasOwnProperty(i)){let o=n[i];Array.isArray(o)||(o=[o],n[i]=o),o.push(s)}else n[i]=s}parseParens(n){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Xt(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new x(4010,!1);let s;r.indexOf(":")>-1?(s=r.slice(0,r.indexOf(":")),this.capture(s),this.capture(":")):n&&(s=g);let o=this.parseChildren();e[s]=Object.keys(o).length===1?o[g]:new S([],o),this.consumeOptional("//")}return e}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new x(4011,!1)}};function Dr(t){return t.segments.length>0?new S([],{[g]:t}):t}function Pr(t){let n={};for(let[r,i]of Object.entries(t.children)){let s=Pr(i);if(r===g&&s.segments.length===0&&s.hasChildren())for(let[o,a]of Object.entries(s.children))n[o]=a;else(s.segments.length>0||s.hasChildren())&&(n[r]=s)}let e=new S(t.segments,n);return Ti(e)}function Ti(t){if(t.numberOfChildren===1&&t.children[g]){let n=t.children[g];return new S(t.segments.concat(n.segments),n.children)}return t}function te(t){return t instanceof V}function _r(t,n,e=null,r=null){let i=Ur(t);return kr(i,n,e,r)}function Ur(t){let n;function e(s){let o={};for(let l of s.children){let c=e(l);o[l.outlet]=c}let a=new S(s.url,o);return s===t&&(n=a),a}let r=e(t.root),i=Dr(r);return n??i}function kr(t,n,e,r){let i=t;for(;i.parent;)i=i.parent;if(n.length===0)return Jt(i,i,i,e,r);let s=Ai(n);if(s.toRoot())return Jt(i,i,new S([],{}),e,r);let o=Mi(s,i,t),a=o.processChildren?$e(o.segmentGroup,o.index,s.commands):jr(o.segmentGroup,o.index,s.commands);return Jt(i,o.segmentGroup,a,e,r)}function ht(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function Fe(t){return typeof t=="object"&&t!=null&&t.outlets}function Jt(t,n,e,r,i){let s={};r&&Object.entries(r).forEach(([l,c])=>{s[l]=Array.isArray(c)?c.map(v=>`${v}`):`${c}`});let o;t===n?o=e:o=Nr(t,n,e);let a=Dr(Pr(o));return new V(a,s,i)}function Nr(t,n,e){let r={};return Object.entries(t.children).forEach(([i,s])=>{s===n?r[i]=e:r[i]=Nr(s,n,e)}),new S(t.segments,r)}var pt=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,e,r){if(this.isAbsolute=n,this.numberOfDoubleDots=e,this.commands=r,n&&r.length>0&&ht(r[0]))throw new x(4003,!1);let i=r.find(Fe);if(i&&i!==xr(r))throw new x(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function Ai(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new pt(!0,0,t);let n=0,e=!1,r=t.reduce((i,s,o)=>{if(typeof s=="object"&&s!=null){if(s.outlets){let a={};return Object.entries(s.outlets).forEach(([l,c])=>{a[l]=typeof c=="string"?c.split("/"):c}),[...i,{outlets:a}]}if(s.segmentPath)return[...i,s.segmentPath]}return typeof s!="string"?[...i,s]:o===0?(s.split("/").forEach((a,l)=>{l==0&&a==="."||(l==0&&a===""?e=!0:a===".."?n++:a!=""&&i.push(a))}),i):[...i,s]},[]);return new pt(e,n,r)}var ve=class{segmentGroup;processChildren;index;constructor(n,e,r){this.segmentGroup=n,this.processChildren=e,this.index=r}};function Mi(t,n,e){if(t.isAbsolute)return new ve(n,!0,0);if(!e)return new ve(n,!1,NaN);if(e.parent===null)return new ve(e,!0,0);let r=ht(t.commands[0])?0:1,i=e.segments.length-1+r;return Oi(e,i,t.numberOfDoubleDots)}function Oi(t,n,e){let r=t,i=n,s=e;for(;s>i;){if(s-=i,r=r.parent,!r)throw new x(4005,!1);i=r.segments.length}return new ve(r,!1,i-s)}function Ei(t){return Fe(t[0])?t[0].outlets:{[g]:t}}function jr(t,n,e){if(t??=new S([],{}),t.segments.length===0&&t.hasChildren())return $e(t,n,e);let r=Di(t,n,e),i=e.slice(r.commandIndex);if(r.match&&r.pathIndex<t.segments.length){let s=new S(t.segments.slice(0,r.pathIndex),{});return s.children[g]=new S(t.segments.slice(r.pathIndex),t.children),$e(s,0,i)}else return r.match&&i.length===0?new S(t.segments,{}):r.match&&!t.hasChildren()?an(t,n,e):r.match?$e(t,0,i):an(t,n,e)}function $e(t,n,e){if(e.length===0)return new S(t.segments,{});{let r=Ei(e),i={};if(Object.keys(r).some(s=>s!==g)&&t.children[g]&&t.numberOfChildren===1&&t.children[g].segments.length===0){let s=$e(t.children[g],n,e);return new S(t.segments,s.children)}return Object.entries(r).forEach(([s,o])=>{typeof o=="string"&&(o=[o]),o!==null&&(i[s]=jr(t.children[s],n,o))}),Object.entries(t.children).forEach(([s,o])=>{r[s]===void 0&&(i[s]=o)}),new S(t.segments,i)}}function Di(t,n,e){let r=0,i=n,s={match:!1,pathIndex:0,commandIndex:0};for(;i<t.segments.length;){if(r>=e.length)return s;let o=t.segments[i],a=e[r];if(Fe(a))break;let l=`${a}`,c=r<e.length-1?e[r+1]:null;if(i>0&&l===void 0)break;if(l&&c&&typeof c=="object"&&c.outlets===void 0){if(!Sr(l,c,o))return s;r+=2}else{if(!Sr(l,{},o))return s;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function an(t,n,e){let r=t.segments.slice(0,n),i=0;for(;i<e.length;){let s=e[i];if(Fe(s)){let l=Pi(s.outlets);return new S(r,l)}if(i===0&&ht(e[0])){let l=t.segments[n];r.push(new ee(l.path,vr(e[0]))),i++;continue}let o=Fe(s)?s.outlets[g]:`${s}`,a=i<e.length-1?e[i+1]:null;o&&a&&ht(a)?(r.push(new ee(o,vr(a))),i+=2):(r.push(new ee(o,{})),i++)}return new S(r,{})}function Pi(t){let n={};return Object.entries(t).forEach(([e,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[e]=an(new S([],{}),0,r))}),n}function vr(t){let n={};return Object.entries(t).forEach(([e,r])=>n[e]=`${r}`),n}function Sr(t,n,e){return t==e.path&&B(n,e.parameters)}var dt="imperative",y=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(y||{}),_=class{id;url;constructor(n,e){this.id=n,this.url=e}},ne=class extends _{type=y.NavigationStart;navigationTrigger;restoredState;constructor(n,e,r="imperative",i=null){super(n,e),this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},M=class extends _{urlAfterRedirects;type=y.NavigationEnd;constructor(n,e,r){super(n,e),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},A=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t}(A||{}),Ce=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(Ce||{}),q=class extends _{reason;code;type=y.NavigationCancel;constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},H=class extends _{reason;code;type=y.NavigationSkipped;constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i}},ye=class extends _{error;target;type=y.NavigationError;constructor(n,e,r,i){super(n,e),this.error=r,this.target=i}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Be=class extends _{urlAfterRedirects;state;type=y.RoutesRecognized;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ft=class extends _{urlAfterRedirects;state;type=y.GuardsCheckStart;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},gt=class extends _{urlAfterRedirects;state;shouldActivate;type=y.GuardsCheckEnd;constructor(n,e,r,i,s){super(n,e),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=s}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},mt=class extends _{urlAfterRedirects;state;type=y.ResolveStart;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},vt=class extends _{urlAfterRedirects;state;type=y.ResolveEnd;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},St=class{route;type=y.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Ct=class{route;type=y.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},yt=class{snapshot;type=y.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},wt=class{snapshot;type=y.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Rt=class{snapshot;type=y.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},bt=class{snapshot;type=y.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},we=class{routerEvent;position;anchor;type=y.Scroll;constructor(n,e,r){this.routerEvent=n,this.position=e,this.anchor=r}toString(){let n=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${n}')`}},qe=class{},Re=class{url;navigationBehaviorOptions;constructor(n,e){this.url=n,this.navigationBehaviorOptions=e}};function _i(t,n){return t.providers&&!t._injector&&(t._injector=Zt(t.providers,n,`Route: ${t.path}`)),t._injector??n}function L(t){return t.outlet||g}function Ui(t,n){let e=t.filter(r=>L(r)===n);return e.push(...t.filter(r=>L(r)!==n)),e}function Ze(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let n=t.parent;n;n=n.parent){let e=n.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var xt=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Ze(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new Ae(this.rootInjector)}},Ae=(()=>{class t{rootInjector;contexts=new Map;constructor(e){this.rootInjector=e}onChildOutletCreated(e,r){let i=this.getOrCreateContext(e);i.outlet=r,this.contexts.set(e,i)}onChildOutletDestroyed(e){let r=this.getContext(e);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let r=this.getContext(e);return r||(r=new xt(this.rootInjector),this.contexts.set(e,r)),r}getContext(e){return this.contexts.get(e)||null}static \u0275fac=function(r){return new(r||t)(fe(Pe))};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),It=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let e=this.pathFromRoot(n);return e.length>1?e[e.length-2]:null}children(n){let e=ln(n,this._root);return e?e.children.map(r=>r.value):[]}firstChild(n){let e=ln(n,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(n){let e=cn(n,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==n)}pathFromRoot(n){return cn(n,this._root).map(e=>e.value)}};function ln(t,n){if(t===n.value)return n;for(let e of n.children){let r=ln(t,e);if(r)return r}return null}function cn(t,n){if(t===n.value)return[n];for(let e of n.children){let r=cn(t,e);if(r.length)return r.unshift(n),r}return[]}var P=class{value;children;constructor(n,e){this.value=n,this.children=e}toString(){return`TreeNode(${this.value})`}};function me(t){let n={};return t&&t.children.forEach(e=>n[e.value.outlet]=e),n}var Ve=class extends It{snapshot;constructor(n,e){super(n),this.snapshot=e,vn(this,n)}toString(){return this.snapshot.toString()}};function Lr(t){let n=ki(t),e=new k([new ee("",{})]),r=new k({}),i=new k({}),s=new k({}),o=new k(""),a=new K(e,r,s,o,i,g,t,n.root);return a.snapshot=n.root,new Ve(new P(a,[]),n)}function ki(t){let n={},e={},r={},i="",s=new ce([],n,r,i,e,g,t,null,{});return new He("",new P(s,[]))}var K=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,e,r,i,s,o,a,l){this.urlSubject=n,this.paramsSubject=e,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=s,this.outlet=o,this.component=a,this._futureSnapshot=l,this.title=this.dataSubject?.pipe(C(c=>c[Ye]))??p(void 0),this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=s}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(C(n=>ue(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(C(n=>ue(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Tt(t,n,e="emptyOnly"){let r,{routeConfig:i}=t;return n!==null&&(e==="always"||i?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:d(d({},n.params),t.params),data:d(d({},n.data),t.data),resolve:d(d(d(d({},t.data),n.data),i?.data),t._resolvedData)}:r={params:d({},t.params),data:d({},t.data),resolve:d(d({},t.data),t._resolvedData??{})},i&&zr(i)&&(r.resolve[Ye]=i.title),r}var ce=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Ye]}constructor(n,e,r,i,s,o,a,l,c){this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=s,this.outlet=o,this.component=a,this.routeConfig=l,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=ue(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=ue(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${e}')`}},He=class extends It{url;constructor(n,e){super(e),this.url=n,vn(this,e)}toString(){return $r(this._root)}};function vn(t,n){n.value._routerState=t,n.children.forEach(e=>vn(t,e))}function $r(t){let n=t.children.length>0?` { ${t.children.map($r).join(", ")} } `:"";return`${t.value}${n}`}function en(t){if(t.snapshot){let n=t.snapshot,e=t._futureSnapshot;t.snapshot=e,B(n.queryParams,e.queryParams)||t.queryParamsSubject.next(e.queryParams),n.fragment!==e.fragment&&t.fragmentSubject.next(e.fragment),B(n.params,e.params)||t.paramsSubject.next(e.params),ci(n.url,e.url)||t.urlSubject.next(e.url),B(n.data,e.data)||t.dataSubject.next(e.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function un(t,n){let e=B(t.params,n.params)&&pi(t.url,n.url),r=!t.parent!=!n.parent;return e&&!r&&(!t.parent||un(t.parent,n.parent))}function zr(t){return typeof t.title=="string"||t.title===null}var Fr=new N(""),Ke=(()=>{class t{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=g;activateEvents=new ae;deactivateEvents=new ae;attachEvents=new ae;detachEvents=new ae;routerOutletData=Vn(void 0);parentContexts=f(Ae);location=f(Qn);changeDetector=f(it);inputBinder=f(Et,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(e){if(e.name){let{firstChange:r,previousValue:i}=e.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new x(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new x(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new x(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,r){this.activated=e,this._activatedRoute=r,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,r){if(this.isActivated)throw new x(4013,!1);this._activatedRoute=e;let i=this.location,o=e.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,l=new dn(e,a,i.injector,this.routerOutletData);this.activated=i.createComponent(o,{index:i.length,injector:l,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||t)};static \u0275dir=Ue({type:t,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[_e]})}return t})(),dn=class{route;childContexts;parent;outletData;constructor(n,e,r,i){this.route=n,this.childContexts=e,this.parent=r,this.outletData=i}get(n,e){return n===K?this.route:n===Ae?this.childContexts:n===Fr?this.outletData:this.parent.get(n,e)}},Et=new N("");var Sn=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275cmp=F({type:t,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,i){r&1&&j(0,"router-outlet")},dependencies:[Ke],encapsulation:2})}return t})();function Cn(t){let n=t.children&&t.children.map(Cn),e=n?U(d({},t),{children:n}):d({},t);return!e.component&&!e.loadComponent&&(n||e.loadChildren)&&e.outlet&&e.outlet!==g&&(e.component=Sn),e}function Ni(t,n,e){let r=We(t,n._root,e?e._root:void 0);return new Ve(r,n)}function We(t,n,e){if(e&&t.shouldReuseRoute(n.value,e.value.snapshot)){let r=e.value;r._futureSnapshot=n.value;let i=ji(t,n,e);return new P(r,i)}else{if(t.shouldAttach(n.value)){let s=t.retrieve(n.value);if(s!==null){let o=s.route;return o.value._futureSnapshot=n.value,o.children=n.children.map(a=>We(t,a)),o}}let r=Li(n.value),i=n.children.map(s=>We(t,s));return new P(r,i)}}function ji(t,n,e){return n.children.map(r=>{for(let i of e.children)if(t.shouldReuseRoute(r.value,i.value.snapshot))return We(t,r,i);return We(t,r)})}function Li(t){return new K(new k(t.url),new k(t.params),new k(t.queryParams),new k(t.fragment),new k(t.data),t.outlet,t.component,t)}var be=class{redirectTo;navigationBehaviorOptions;constructor(n,e){this.redirectTo=n,this.navigationBehaviorOptions=e}},Br="ngNavigationCancelingError";function At(t,n){let{redirectTo:e,navigationBehaviorOptions:r}=te(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,i=qr(!1,A.Redirect);return i.url=e,i.navigationBehaviorOptions=r,i}function qr(t,n){let e=new Error(`NavigationCancelingError: ${t||""}`);return e[Br]=!0,e.cancellationCode=n,e}function $i(t){return Vr(t)&&te(t.url)}function Vr(t){return!!t&&t[Br]}var zi=(t,n,e,r)=>C(i=>(new hn(n,i.targetRouterState,i.currentRouterState,e,r).activate(t),i)),hn=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,e,r,i,s){this.routeReuseStrategy=n,this.futureState=e,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=s}activate(n){let e=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,r,n),en(this.futureState.root),this.activateChildRoutes(e,r,n)}deactivateChildRoutes(n,e,r){let i=me(e);n.children.forEach(s=>{let o=s.value.outlet;this.deactivateRoutes(s,i[o],r),delete i[o]}),Object.values(i).forEach(s=>{this.deactivateRouteAndItsChildren(s,r)})}deactivateRoutes(n,e,r){let i=n.value,s=e?e.value:null;if(i===s)if(i.component){let o=r.getContext(i.outlet);o&&this.deactivateChildRoutes(n,e,o.children)}else this.deactivateChildRoutes(n,e,r);else s&&this.deactivateRouteAndItsChildren(e,r)}deactivateRouteAndItsChildren(n,e){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,e):this.deactivateRouteAndOutlet(n,e)}detachAndStoreRouteSubtree(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,s=me(n);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);if(r&&r.outlet){let o=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:o,route:n,contexts:a})}}deactivateRouteAndOutlet(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,s=me(n);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,e,r){let i=me(e);n.children.forEach(s=>{this.activateRoutes(s,i[s.value.outlet],r),this.forwardEvent(new bt(s.value.snapshot))}),n.children.length&&this.forwardEvent(new wt(n.value.snapshot))}activateRoutes(n,e,r){let i=n.value,s=e?e.value:null;if(en(i),i===s)if(i.component){let o=r.getOrCreateContext(i.outlet);this.activateChildRoutes(n,e,o.children)}else this.activateChildRoutes(n,e,r);else if(i.component){let o=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),o.children.onOutletReAttached(a.contexts),o.attachRef=a.componentRef,o.route=a.route.value,o.outlet&&o.outlet.attach(a.componentRef,a.route.value),en(a.route.value),this.activateChildRoutes(n,null,o.children)}else o.attachRef=null,o.route=i,o.outlet&&o.outlet.activateWith(i,o.injector),this.activateChildRoutes(n,null,o.children)}else this.activateChildRoutes(n,null,r)}},Mt=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Se=class{component;route;constructor(n,e){this.component=n,this.route=e}};function Fi(t,n,e){let r=t._root,i=n?n._root:null;return Le(r,i,e,[r.value])}function Bi(t){let n=t.routeConfig?t.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:t,guards:n}}function Me(t,n){let e=Symbol(),r=n.get(t,e);return r===e?typeof t=="function"&&!Nn(t)?t:n.get(t):r}function Le(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=me(n);return t.children.forEach(o=>{qi(o,s[o.value.outlet],e,r.concat([o.value]),i),delete s[o.value.outlet]}),Object.entries(s).forEach(([o,a])=>ze(a,e.getContext(o),i)),i}function qi(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=t.value,o=n?n.value:null,a=e?e.getContext(t.value.outlet):null;if(o&&s.routeConfig===o.routeConfig){let l=Vi(o,s,s.routeConfig.runGuardsAndResolvers);l?i.canActivateChecks.push(new Mt(r)):(s.data=o.data,s._resolvedData=o._resolvedData),s.component?Le(t,n,a?a.children:null,r,i):Le(t,n,e,r,i),l&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new Se(a.outlet.component,o))}else o&&ze(n,a,i),i.canActivateChecks.push(new Mt(r)),s.component?Le(t,null,a?a.children:null,r,i):Le(t,null,e,r,i);return i}function Vi(t,n,e){if(typeof e=="function")return e(t,n);switch(e){case"pathParamsChange":return!le(t.url,n.url);case"pathParamsOrQueryParamsChange":return!le(t.url,n.url)||!B(t.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!un(t,n)||!B(t.queryParams,n.queryParams);case"paramsChange":default:return!un(t,n)}}function ze(t,n,e){let r=me(t),i=t.value;Object.entries(r).forEach(([s,o])=>{i.component?n?ze(o,n.children.getContext(s),e):ze(o,null,e):ze(o,n,e)}),i.component?n&&n.outlet&&n.outlet.isActivated?e.canDeactivateChecks.push(new Se(n.outlet.component,i)):e.canDeactivateChecks.push(new Se(null,i)):e.canDeactivateChecks.push(new Se(null,i))}function Xe(t){return typeof t=="function"}function Hi(t){return typeof t=="boolean"}function Wi(t){return t&&Xe(t.canLoad)}function Gi(t){return t&&Xe(t.canActivate)}function Qi(t){return t&&Xe(t.canActivateChild)}function Yi(t){return t&&Xe(t.canDeactivate)}function Zi(t){return t&&Xe(t.canMatch)}function Hr(t){return t instanceof En||t?.name==="EmptyError"}var at=Symbol("INITIAL_VALUE");function xe(){return z(t=>zt(t.map(n=>n.pipe(pe(1),Un(at)))).pipe(C(n=>{for(let e of n)if(e!==!0){if(e===at)return at;if(e===!1||Ki(e))return e}return!0}),Y(n=>n!==at),pe(1)))}function Ki(t){return te(t)||t instanceof be}function Xi(t,n){return D(e=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:s,canDeactivateChecks:o}}=e;return o.length===0&&s.length===0?p(U(d({},e),{guardsResult:!0})):Ji(o,r,i,t).pipe(D(a=>a&&Hi(a)?eo(r,s,t,n):p(a)),C(a=>U(d({},e),{guardsResult:a})))})}function Ji(t,n,e,r){return E(t).pipe(D(i=>oo(i.component,i.route,e,n,r)),J(i=>i!==!0,!0))}function eo(t,n,e,r){return E(n).pipe(he(i=>Dn(no(i.route.parent,r),to(i.route,r),io(t,i.path,e),ro(t,i.route,e))),J(i=>i!==!0,!0))}function to(t,n){return t!==null&&n&&n(new Rt(t)),p(!0)}function no(t,n){return t!==null&&n&&n(new yt(t)),p(!0)}function ro(t,n,e){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return p(!0);let i=r.map(s=>Bt(()=>{let o=Ze(n)??e,a=Me(s,o),l=Gi(a)?a.canActivate(n,t):Z(o,()=>a(n,t));return re(l).pipe(J())}));return p(i).pipe(xe())}function io(t,n,e){let r=n[n.length-1],s=n.slice(0,n.length-1).reverse().map(o=>Bi(o)).filter(o=>o!==null).map(o=>Bt(()=>{let a=o.guards.map(l=>{let c=Ze(o.node)??e,v=Me(l,c),R=Qi(v)?v.canActivateChild(r,t):Z(c,()=>v(r,t));return re(R).pipe(J())});return p(a).pipe(xe())}));return p(s).pipe(xe())}function oo(t,n,e,r,i){let s=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!s||s.length===0)return p(!0);let o=s.map(a=>{let l=Ze(n)??i,c=Me(a,l),v=Yi(c)?c.canDeactivate(t,n,e,r):Z(l,()=>c(t,n,e,r));return re(v).pipe(J())});return p(o).pipe(xe())}function so(t,n,e,r){let i=n.canLoad;if(i===void 0||i.length===0)return p(!0);let s=i.map(o=>{let a=Me(o,t),l=Wi(a)?a.canLoad(n,e):Z(t,()=>a(n,e));return re(l)});return p(s).pipe(xe(),Wr(r))}function Wr(t){return Mn(b(n=>{if(typeof n!="boolean")throw At(t,n)}),C(n=>n===!0))}function ao(t,n,e,r){let i=n.canMatch;if(!i||i.length===0)return p(!0);let s=i.map(o=>{let a=Me(o,t),l=Zi(a)?a.canMatch(n,e):Z(t,()=>a(n,e));return re(l)});return p(s).pipe(xe(),Wr(r))}var Ge=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},Qe=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function ge(t){return De(new Ge(t))}function lo(t){return De(new x(4e3,!1))}function co(t){return De(qr(!1,A.GuardRejected))}var pn=class{urlSerializer;urlTree;constructor(n,e){this.urlSerializer=n,this.urlTree=e}lineralizeSegments(n,e){let r=[],i=e.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return p(r);if(i.numberOfChildren>1||!i.children[g])return lo(`${n.redirectTo}`);i=i.children[g]}}applyRedirectCommands(n,e,r,i,s){if(typeof e!="string"){let a=e,{queryParams:l,fragment:c,routeConfig:v,url:R,outlet:O,params:$,data:I,title:X}=i,W=Z(s,()=>a({params:$,data:I,queryParams:l,fragment:c,routeConfig:v,url:R,outlet:O,title:X}));if(W instanceof V)throw new Qe(W);e=W}let o=this.applyRedirectCreateUrlTree(e,this.urlSerializer.parse(e),n,r);if(e[0]==="/")throw new Qe(o);return o}applyRedirectCreateUrlTree(n,e,r,i){let s=this.createSegmentGroup(n,e.root,r,i);return new V(s,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(n,e){let r={};return Object.entries(n).forEach(([i,s])=>{if(typeof s=="string"&&s[0]===":"){let a=s.substring(1);r[i]=e[a]}else r[i]=s}),r}createSegmentGroup(n,e,r,i){let s=this.createSegments(n,e.segments,r,i),o={};return Object.entries(e.children).forEach(([a,l])=>{o[a]=this.createSegmentGroup(n,l,r,i)}),new S(s,o)}createSegments(n,e,r,i){return e.map(s=>s.path[0]===":"?this.findPosParam(n,s,i):this.findOrReturn(s,r))}findPosParam(n,e,r){let i=r[e.path.substring(1)];if(!i)throw new x(4001,!1);return i}findOrReturn(n,e){let r=0;for(let i of e){if(i.path===n.path)return e.splice(r),i;r++}return n}},fn={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function uo(t,n,e,r,i){let s=Gr(t,n,e);return s.matched?(r=_i(n,r),ao(r,n,e,i).pipe(C(o=>o===!0?s:d({},fn)))):p(s)}function Gr(t,n,e){if(n.path==="**")return ho(e);if(n.path==="")return n.pathMatch==="full"&&(t.hasChildren()||e.length>0)?d({},fn):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let i=(n.matcher||Rr)(e,t,n);if(!i)return d({},fn);let s={};Object.entries(i.posParams??{}).forEach(([a,l])=>{s[a]=l.path});let o=i.consumed.length>0?d(d({},s),i.consumed[i.consumed.length-1].parameters):s;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:o,positionalParamSegments:i.posParams??{}}}function ho(t){return{matched:!0,parameters:t.length>0?xr(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function Cr(t,n,e,r){return e.length>0&&go(t,e,r)?{segmentGroup:new S(n,fo(r,new S(e,t.children))),slicedSegments:[]}:e.length===0&&mo(t,e,r)?{segmentGroup:new S(t.segments,po(t,e,r,t.children)),slicedSegments:e}:{segmentGroup:new S(t.segments,t.children),slicedSegments:e}}function po(t,n,e,r){let i={};for(let s of e)if(Dt(t,n,s)&&!r[L(s)]){let o=new S([],{});i[L(s)]=o}return d(d({},r),i)}function fo(t,n){let e={};e[g]=n;for(let r of t)if(r.path===""&&L(r)!==g){let i=new S([],{});e[L(r)]=i}return e}function go(t,n,e){return e.some(r=>Dt(t,n,r)&&L(r)!==g)}function mo(t,n,e){return e.some(r=>Dt(t,n,r))}function Dt(t,n,e){return(t.hasChildren()||n.length>0)&&e.pathMatch==="full"?!1:e.path===""}function vo(t,n,e){return n.length===0&&!t.children[e]}var gn=class{};function So(t,n,e,r,i,s,o="emptyOnly"){return new mn(t,n,e,r,i,o,s).recognize()}var Co=31,mn=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,e,r,i,s,o,a){this.injector=n,this.configLoader=e,this.rootComponentType=r,this.config=i,this.urlTree=s,this.paramsInheritanceStrategy=o,this.urlSerializer=a,this.applyRedirects=new pn(this.urlSerializer,this.urlTree)}noMatchError(n){return new x(4002,`'${n.segmentGroup}'`)}recognize(){let n=Cr(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(C(({children:e,rootSnapshot:r})=>{let i=new P(r,e),s=new He("",i),o=_r(r,[],this.urlTree.queryParams,this.urlTree.fragment);return o.queryParams=this.urlTree.queryParams,s.url=this.urlSerializer.serialize(o),{state:s,tree:o}}))}match(n){let e=new ce([],Object.freeze({}),Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),g,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,g,e).pipe(C(r=>({children:r,rootSnapshot:e})),oe(r=>{if(r instanceof Qe)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Ge?this.noMatchError(r):r}))}processSegmentGroup(n,e,r,i,s){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,e,r,s):this.processSegment(n,e,r,r.segments,i,!0,s).pipe(C(o=>o instanceof P?[o]:[]))}processChildren(n,e,r,i){let s=[];for(let o of Object.keys(r.children))o==="primary"?s.unshift(o):s.push(o);return E(s).pipe(he(o=>{let a=r.children[o],l=Ui(e,o);return this.processSegmentGroup(n,l,a,o,i)}),_n((o,a)=>(o.push(...a),o)),qt(null),Pn(),D(o=>{if(o===null)return ge(r);let a=Qr(o);return yo(a),p(a)}))}processSegment(n,e,r,i,s,o,a){return E(e).pipe(he(l=>this.processSegmentAgainstRoute(l._injector??n,e,l,r,i,s,o,a).pipe(oe(c=>{if(c instanceof Ge)return p(null);throw c}))),J(l=>!!l),oe(l=>{if(Hr(l))return vo(r,i,s)?p(new gn):ge(r);throw l}))}processSegmentAgainstRoute(n,e,r,i,s,o,a,l){return L(r)!==o&&(o===g||!Dt(i,s,r))?ge(i):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,i,r,s,o,l):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,i,e,r,s,o,l):ge(i)}expandSegmentAgainstRouteUsingRedirect(n,e,r,i,s,o,a){let{matched:l,parameters:c,consumedSegments:v,positionalParamSegments:R,remainingSegments:O}=Gr(e,i,s);if(!l)return ge(e);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Co&&(this.allowRedirects=!1));let $=new ce(s,c,Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,yr(i),L(i),i.component??i._loadedComponent??null,i,wr(i)),I=Tt($,a,this.paramsInheritanceStrategy);$.params=Object.freeze(I.params),$.data=Object.freeze(I.data);let X=this.applyRedirects.applyRedirectCommands(v,i.redirectTo,R,$,n);return this.applyRedirects.lineralizeSegments(i,X).pipe(D(W=>this.processSegment(n,r,e,W.concat(O),o,!1,a)))}matchSegmentAgainstRoute(n,e,r,i,s,o){let a=uo(e,r,i,n,this.urlSerializer);return r.path==="**"&&(e.children={}),a.pipe(z(l=>l.matched?(n=r._injector??n,this.getChildConfig(n,r,i).pipe(z(({routes:c})=>{let v=r._loadedInjector??n,{parameters:R,consumedSegments:O,remainingSegments:$}=l,I=new ce(O,R,Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,yr(r),L(r),r.component??r._loadedComponent??null,r,wr(r)),X=Tt(I,o,this.paramsInheritanceStrategy);I.params=Object.freeze(X.params),I.data=Object.freeze(X.data);let{segmentGroup:W,slicedSegments:jt}=Cr(e,O,$,c);if(jt.length===0&&W.hasChildren())return this.processChildren(v,c,W,I).pipe(C(tt=>new P(I,tt)));if(c.length===0&&jt.length===0)return p(new P(I,[]));let li=L(r)===s;return this.processSegment(v,c,W,jt,li?g:s,!0,I).pipe(C(tt=>new P(I,tt instanceof P?[tt]:[])))}))):ge(e)))}getChildConfig(n,e,r){return e.children?p({routes:e.children,injector:n}):e.loadChildren?e._loadedRoutes!==void 0?p({routes:e._loadedRoutes,injector:e._loadedInjector}):so(n,e,r,this.urlSerializer).pipe(D(i=>i?this.configLoader.loadChildren(n,e).pipe(b(s=>{e._loadedRoutes=s.routes,e._loadedInjector=s.injector})):co(e))):p({routes:[],injector:n})}};function yo(t){t.sort((n,e)=>n.value.outlet===g?-1:e.value.outlet===g?1:n.value.outlet.localeCompare(e.value.outlet))}function wo(t){let n=t.value.routeConfig;return n&&n.path===""}function Qr(t){let n=[],e=new Set;for(let r of t){if(!wo(r)){n.push(r);continue}let i=n.find(s=>r.value.routeConfig===s.value.routeConfig);i!==void 0?(i.children.push(...r.children),e.add(i)):n.push(r)}for(let r of e){let i=Qr(r.children);n.push(new P(r.value,i))}return n.filter(r=>!e.has(r))}function yr(t){return t.data||{}}function wr(t){return t.resolve||{}}function Ro(t,n,e,r,i,s){return D(o=>So(t,n,e,r,o.extractedUrl,i,s).pipe(C(({state:a,tree:l})=>U(d({},o),{targetSnapshot:a,urlAfterRedirects:l}))))}function bo(t,n){return D(e=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=e;if(!i.length)return p(e);let s=new Set(i.map(l=>l.route)),o=new Set;for(let l of s)if(!o.has(l))for(let c of Yr(l))o.add(c);let a=0;return E(o).pipe(he(l=>s.has(l)?xo(l,r,t,n):(l.data=Tt(l,l.parent,t).resolve,p(void 0))),b(()=>a++),Vt(1),D(l=>a===o.size?p(e):Q))})}function Yr(t){let n=t.children.map(e=>Yr(e)).flat();return[t,...n]}function xo(t,n,e,r){let i=t.routeConfig,s=t._resolve;return i?.title!==void 0&&!zr(i)&&(s[Ye]=i.title),Io(s,t,n,r).pipe(C(o=>(t._resolvedData=o,t.data=Tt(t,t.parent,e).resolve,null)))}function Io(t,n,e,r){let i=rn(t);if(i.length===0)return p({});let s={};return E(i).pipe(D(o=>To(t[o],n,e,r).pipe(J(),b(a=>{if(a instanceof be)throw At(new de,a);s[o]=a}))),Vt(1),C(()=>s),oe(o=>Hr(o)?Q:De(o)))}function To(t,n,e,r){let i=Ze(n)??r,s=Me(t,i),o=s.resolve?s.resolve(n,e):Z(i,()=>s(n,e));return re(o)}function tn(t){return z(n=>{let e=t(n);return e?E(e).pipe(C(()=>n)):p(n)})}var yn=(()=>{class t{buildTitle(e){let r,i=e.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(s=>s.outlet===g);return r}getResolvedTitleForRoute(e){return e.data[Ye]}static \u0275fac=function(r){return new(r||t)};static \u0275prov=w({token:t,factory:()=>f(Zr),providedIn:"root"})}return t})(),Zr=(()=>{class t extends yn{title;constructor(e){super(),this.title=e}updateTitle(e){let r=this.buildTitle(e);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||t)(fe(fr))};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Je=new N("",{providedIn:"root",factory:()=>({})}),et=new N(""),Kr=(()=>{class t{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=f(lr);loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return p(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let r=re(e.loadComponent()).pipe(C(Jr),b(s=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=s}),nt(()=>{this.componentLoaders.delete(e)})),i=new $t(r,()=>new G).pipe(Lt());return this.componentLoaders.set(e,i),i}loadChildren(e,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return p({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let s=Xr(r,this.compiler,e,this.onLoadEndListener).pipe(nt(()=>{this.childrenLoaders.delete(r)})),o=new $t(s,()=>new G).pipe(Lt());return this.childrenLoaders.set(r,o),o}static \u0275fac=function(r){return new(r||t)};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Xr(t,n,e,r){return re(t.loadChildren()).pipe(C(Jr),D(i=>i instanceof Yn||Array.isArray(i)?p(i):E(n.compileModuleAsync(i))),C(i=>{r&&r(t);let s,o,a=!1;return Array.isArray(i)?(o=i,a=!0):(s=i.create(e).injector,o=s.get(et,[],{optional:!0,self:!0}).flat()),{routes:o.map(Cn),injector:s}}))}function Ao(t){return t&&typeof t=="object"&&"default"in t}function Jr(t){return Ao(t)?t.default:t}var Pt=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=w({token:t,factory:()=>f(Mo),providedIn:"root"})}return t})(),Mo=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,r){return e}static \u0275fac=function(r){return new(r||t)};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ei=new N("");var ti=new N(""),wn=(()=>{class t{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new G;transitionAbortSubject=new G;configLoader=f(Kr);environmentInjector=f(Pe);destroyRef=f(Bn);urlSerializer=f(Te);rootContexts=f(Ae);location=f(Ne);inputBindingEnabled=f(Et,{optional:!0})!==null;titleStrategy=f(yn);options=f(Je,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=f(Pt);createViewTransition=f(ei,{optional:!0});navigationErrorHandler=f(ti,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>p(void 0);rootComponentType=null;destroyed=!1;constructor(){let e=i=>this.events.next(new St(i)),r=i=>this.events.next(new Ct(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=e,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(e){let r=++this.navigationId;this.transitions?.next(U(d({},e),{extractedUrl:this.urlHandlingStrategy.extract(e.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(e){return this.transitions=new k(null),this.transitions.pipe(Y(r=>r!==null),z(r=>{let i=!1,s=!1;return p(r).pipe(z(o=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",A.SupersededByNewNavigation),Q;this.currentTransition=r,this.currentNavigation={id:o.id,initialUrl:o.rawUrl,extractedUrl:o.extractedUrl,targetBrowserUrl:typeof o.extras.browserUrl=="string"?this.urlSerializer.parse(o.extras.browserUrl):o.extras.browserUrl,trigger:o.source,extras:o.extras,previousNavigation:this.lastSuccessfulNavigation?U(d({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),l=o.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!a&&l!=="reload"){let c="";return this.events.next(new H(o.id,this.urlSerializer.serialize(o.rawUrl),c,Ce.IgnoredSameUrlNavigation)),o.resolve(!1),Q}if(this.urlHandlingStrategy.shouldProcessUrl(o.rawUrl))return p(o).pipe(z(c=>(this.events.next(new ne(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?Q:Promise.resolve(c))),Ro(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),b(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=U(d({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let v=new Be(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(v)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(o.currentRawUrl)){let{id:c,extractedUrl:v,source:R,restoredState:O,extras:$}=o,I=new ne(c,this.urlSerializer.serialize(v),R,O);this.events.next(I);let X=Lr(this.rootComponentType).snapshot;return this.currentTransition=r=U(d({},o),{targetSnapshot:X,urlAfterRedirects:v,extras:U(d({},$),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=v,p(r)}else{let c="";return this.events.next(new H(o.id,this.urlSerializer.serialize(o.extractedUrl),c,Ce.IgnoredByUrlHandlingStrategy)),o.resolve(!1),Q}}),b(o=>{let a=new ft(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects),o.targetSnapshot);this.events.next(a)}),C(o=>(this.currentTransition=r=U(d({},o),{guards:Fi(o.targetSnapshot,o.currentSnapshot,this.rootContexts)}),r)),Xi(this.environmentInjector,o=>this.events.next(o)),b(o=>{if(r.guardsResult=o.guardsResult,o.guardsResult&&typeof o.guardsResult!="boolean")throw At(this.urlSerializer,o.guardsResult);let a=new gt(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects),o.targetSnapshot,!!o.guardsResult);this.events.next(a)}),Y(o=>o.guardsResult?!0:(this.cancelNavigationTransition(o,"",A.GuardRejected),!1)),tn(o=>{if(o.guards.canActivateChecks.length!==0)return p(o).pipe(b(a=>{let l=new mt(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(l)}),z(a=>{let l=!1;return p(a).pipe(bo(this.paramsInheritanceStrategy,this.environmentInjector),b({next:()=>l=!0,complete:()=>{l||this.cancelNavigationTransition(a,"",A.NoDataFromResolver)}}))}),b(a=>{let l=new vt(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(l)}))}),tn(o=>{let a=l=>{let c=[];l.routeConfig?.loadComponent&&!l.routeConfig._loadedComponent&&c.push(this.configLoader.loadComponent(l.routeConfig).pipe(b(v=>{l.component=v}),C(()=>{})));for(let v of l.children)c.push(...a(v));return c};return zt(a(o.targetSnapshot.root)).pipe(qt(null),pe(1))}),tn(()=>this.afterPreactivation()),z(()=>{let{currentSnapshot:o,targetSnapshot:a}=r,l=this.createViewTransition?.(this.environmentInjector,o.root,a.root);return l?E(l).pipe(C(()=>r)):p(r)}),C(o=>{let a=Ni(e.routeReuseStrategy,o.targetSnapshot,o.currentRouterState);return this.currentTransition=r=U(d({},o),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),b(()=>{this.events.next(new qe)}),zi(this.rootContexts,e.routeReuseStrategy,o=>this.events.next(o),this.inputBindingEnabled),pe(1),b({next:o=>{i=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new M(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects))),this.titleStrategy?.updateTitle(o.targetRouterState.snapshot),o.resolve(!0)},complete:()=>{i=!0}}),kn(this.transitionAbortSubject.pipe(b(o=>{throw o}))),nt(()=>{!i&&!s&&this.cancelNavigationTransition(r,"",A.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),oe(o=>{if(this.destroyed)return r.resolve(!1),Q;if(s=!0,Vr(o))this.events.next(new q(r.id,this.urlSerializer.serialize(r.extractedUrl),o.message,o.cancellationCode)),$i(o)?this.events.next(new Re(o.url,o.navigationBehaviorOptions)):r.resolve(!1);else{let a=new ye(r.id,this.urlSerializer.serialize(r.extractedUrl),o,r.targetSnapshot??void 0);try{let l=Z(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(l instanceof be){let{message:c,cancellationCode:v}=At(this.urlSerializer,l);this.events.next(new q(r.id,this.urlSerializer.serialize(r.extractedUrl),c,v)),this.events.next(new Re(l.redirectTo,l.navigationBehaviorOptions))}else throw this.events.next(a),o}catch(l){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(l)}}return Q}))}))}cancelNavigationTransition(e,r,i){let s=new q(e.id,this.urlSerializer.serialize(e.extractedUrl),r,i);this.events.next(s),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let e=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return e.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||t)};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Oo(t){return t!==dt}var ni=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=w({token:t,factory:()=>f(Eo),providedIn:"root"})}return t})(),Ot=class{shouldDetach(n){return!1}store(n,e){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,e){return n.routeConfig===e.routeConfig}},Eo=(()=>{class t extends Ot{static \u0275fac=(()=>{let e;return function(i){return(e||(e=Wt(t)))(i||t)}})();static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ri=(()=>{class t{urlSerializer=f(Te);options=f(Je,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=f(Ne);urlHandlingStrategy=f(Pt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new V;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:e,initialUrl:r,targetBrowserUrl:i}){let s=e!==void 0?this.urlHandlingStrategy.merge(e,r):r,o=i??s;return o instanceof V?this.urlSerializer.serialize(o):o}commitTransition({targetRouterState:e,finalUrl:r,initialUrl:i}){r&&e?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,i),this.routerState=e):this.rawUrlTree=i}routerState=Lr(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:e}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e??this.rawUrlTree)}static \u0275fac=function(r){return new(r||t)};static \u0275prov=w({token:t,factory:()=>f(Do),providedIn:"root"})}return t})(),Do=(()=>{class t extends ri{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{e(r.url,r.state,"popstate")})})}handleRouterEvent(e,r){e instanceof ne?this.updateStateMemento():e instanceof H?this.commitTransition(r):e instanceof Be?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof qe?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof q&&(e.code===A.GuardRejected||e.code===A.NoDataFromResolver)?this.restoreHistory(r):e instanceof ye?this.restoreHistory(r,!0):e instanceof M&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,{extras:r,id:i}){let{replaceUrl:s,state:o}=r;if(this.location.isCurrentPathEqualTo(e)||s){let a=this.browserPageId,l=d(d({},o),this.generateNgRouterState(i,a));this.location.replaceState(e,"",l)}else{let a=d(d({},o),this.generateNgRouterState(i,this.browserPageId+1));this.location.go(e,"",a)}}restoreHistory(e,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,s=this.currentPageId-i;s!==0?this.location.historyGo(s):this.getCurrentUrlTree()===e.finalUrl&&s===0&&(this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,r){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:r}:{navigationId:e}}static \u0275fac=(()=>{let e;return function(i){return(e||(e=Wt(t)))(i||t)}})();static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Rn(t,n){t.events.pipe(Y(e=>e instanceof M||e instanceof q||e instanceof ye||e instanceof H),C(e=>e instanceof M||e instanceof H?0:(e instanceof q?e.code===A.Redirect||e.code===A.SupersededByNewNavigation:!1)?2:1),Y(e=>e!==2),pe(1)).subscribe(()=>{n()})}var Po={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},_o={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},ie=(()=>{class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=f(Kn);stateManager=f(ri);options=f(Je,{optional:!0})||{};pendingTasks=f(qn);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=f(wn);urlSerializer=f(Te);location=f(Ne);urlHandlingStrategy=f(Pt);_events=new G;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=f(ni);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=f(et,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!f(Et,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}eventsSubscription=new An;subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,s=this.navigationTransitions.currentNavigation;if(i!==null&&s!==null){if(this.stateManager.handleRouterEvent(r,s),r instanceof q&&r.code!==A.Redirect&&r.code!==A.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof M)this.navigated=!0;else if(r instanceof Re){let o=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),l=d({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||Oo(i.source)},o);this.scheduleNavigation(a,dt,null,l,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}ko(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),dt,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,r,i)=>{this.navigateToSyncWithBrowser(e,i,r)})}navigateToSyncWithBrowser(e,r,i){let s={replaceUrl:!0},o=i?.navigationId?i:null;if(i){let l=d({},i);delete l.navigationId,delete l.\u0275routerPageId,Object.keys(l).length!==0&&(s.state=l)}let a=this.parseUrl(e);this.scheduleNavigation(a,r,o,s)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(Cn),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,r={}){let{relativeTo:i,queryParams:s,fragment:o,queryParamsHandling:a,preserveFragment:l}=r,c=l?this.currentUrlTree.fragment:o,v=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":v=d(d({},this.currentUrlTree.queryParams),s);break;case"preserve":v=this.currentUrlTree.queryParams;break;default:v=s||null}v!==null&&(v=this.removeEmptyProps(v));let R;try{let O=i?i.snapshot:this.routerState.snapshot.root;R=Ur(O)}catch{(typeof e[0]!="string"||e[0][0]!=="/")&&(e=[]),R=this.currentUrlTree.root}return kr(R,e,v,c??null)}navigateByUrl(e,r={skipLocationChange:!1}){let i=te(e)?e:this.parseUrl(e),s=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(s,dt,null,r)}navigate(e,r={skipLocationChange:!1}){return Uo(e),this.navigateByUrl(this.createUrlTree(e,r),r)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,r){let i;if(r===!0?i=d({},Po):r===!1?i=d({},_o):i=r,te(e))return gr(this.currentUrlTree,e,i);let s=this.parseUrl(e);return gr(this.currentUrlTree,s,i)}removeEmptyProps(e){return Object.entries(e).reduce((r,[i,s])=>(s!=null&&(r[i]=s),r),{})}scheduleNavigation(e,r,i,s,o){if(this.disposed)return Promise.resolve(!1);let a,l,c;o?(a=o.resolve,l=o.reject,c=o.promise):c=new Promise((R,O)=>{a=R,l=O});let v=this.pendingTasks.add();return Rn(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(v))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:s,resolve:a,reject:l,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(R=>Promise.reject(R))}static \u0275fac=function(r){return new(r||t)};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Uo(t){for(let n=0;n<t.length;n++)if(t[n]==null)throw new x(4008,!1)}function ko(t){return!(t instanceof qe)&&!(t instanceof Re)}var Oe=(()=>{class t{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new G;constructor(e,r,i,s,o,a){this.router=e,this.route=r,this.tabIndexAttribute=i,this.renderer=s,this.el=o,this.locationStrategy=a;let l=o.nativeElement.tagName?.toLowerCase();this.isAnchorElement=l==="a"||l==="area",this.isAnchorElement?this.subscription=e.events.subscribe(c=>{c instanceof M&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(e){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(e){e==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(te(e)?this.routerLinkInput=e:this.routerLinkInput=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0"))}onClick(e,r,i,s,o){let a=this.urlTree;if(a===null||this.isAnchorElement&&(e!==0||r||i||s||o||typeof this.target=="string"&&this.target!="_self"))return!0;let l={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,l),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let e=this.urlTree;this.href=e!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(e)):null;let r=this.href===null?null:Hn(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(e,r){let i=this.renderer,s=this.el.nativeElement;r!==null?i.setAttribute(s,e,r):i.removeAttribute(s,e)}get urlTree(){return this.routerLinkInput===null?null:te(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||t)(T(ie),T(K),zn("tabindex"),T(Yt),T(Qt),T(Kt))};static \u0275dir=Ue({type:t,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,i){r&1&&ke("click",function(o){return i.onClick(o.button,o.ctrlKey,o.shiftKey,o.altKey,o.metaKey)}),r&2&&tr("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",ot],skipLocationChange:[2,"skipLocationChange","skipLocationChange",ot],replaceUrl:[2,"replaceUrl","replaceUrl",ot],routerLink:"routerLink"},features:[_e]})}return t})(),bn=(()=>{class t{router;element;renderer;cdr;link;links;classes=[];routerEventsSubscription;linkInputChangesSubscription;_isActive=!1;get isActive(){return this._isActive}routerLinkActiveOptions={exact:!1};ariaCurrentWhenActive;isActiveChange=new ae;constructor(e,r,i,s,o){this.router=e,this.element=r,this.renderer=i,this.cdr=s,this.link=o,this.routerEventsSubscription=e.events.subscribe(a=>{a instanceof M&&this.update()})}ngAfterContentInit(){p(this.links.changes,p(null)).pipe(Ft()).subscribe(e=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let e=[...this.links.toArray(),this.link].filter(r=>!!r).map(r=>r.onChanges);this.linkInputChangesSubscription=E(e).pipe(Ft()).subscribe(r=>{this._isActive!==this.isLinkActive(this.router)(r)&&this.update()})}set routerLinkActive(e){let r=Array.isArray(e)?e:e.split(" ");this.classes=r.filter(i=>!!i)}ngOnChanges(e){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let e=this.hasActiveLinks();this.classes.forEach(r=>{e?this.renderer.addClass(this.element.nativeElement,r):this.renderer.removeClass(this.element.nativeElement,r)}),e&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==e&&(this._isActive=e,this.cdr.markForCheck(),this.isActiveChange.emit(e))})}isLinkActive(e){let r=jo(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return i=>{let s=i.urlTree;return s?e.isActive(s,r):!1}}hasActiveLinks(){let e=this.isLinkActive(this.router);return this.link&&e(this.link)||this.links.some(e)}static \u0275fac=function(r){return new(r||t)(T(ie),T(Qt),T(Yt),T(it),T(Oe,8))};static \u0275dir=Ue({type:t,selectors:[["","routerLinkActive",""]],contentQueries:function(r,i,s){if(r&1&&or(s,Oe,5),r&2){let o;sr(o=ar())&&(i.links=o)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],features:[_e]})}return t})();function jo(t){return!!t.paths}var ii=new N(""),Lo=(()=>{class t{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(e,r,i,s,o={}){this.urlSerializer=e,this.transitions=r,this.viewportScroller=i,this.zone=s,this.options=o,o.scrollPositionRestoration||="disabled",o.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof ne?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=e.navigationTrigger,this.restoredId=e.restoredState?e.restoredState.navigationId:0):e instanceof M?(this.lastId=e.id,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.urlAfterRedirects).fragment)):e instanceof H&&e.code===Ce.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof we&&(e.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(e.position):e.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(e.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(e,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new we(e,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){Gn()};static \u0275prov=w({token:t,factory:t.\u0275fac})}return t})();function xn(t,...n){return jn([{provide:et,multi:!0,useValue:t},[],{provide:K,useFactory:$o,deps:[ie]},{provide:Jn,multi:!0,useFactory:Fo},n.map(e=>e.\u0275providers)])}function $o(t){return t.routerState.root}function zo(t,n){return{\u0275kind:t,\u0275providers:n}}function In(t={}){return zo(4,[{provide:ii,useFactory:()=>{let e=f(hr),r=f(Gt),i=f(wn),s=f(Te);return new Lo(s,i,e,r,t)}}])}function Fo(){let t=f(Fn);return n=>{let e=t.get(er);if(n!==e.components[0])return;let r=t.get(ie),i=t.get(Bo);t.get(qo)===1&&r.initialNavigation(),t.get(Vo,null,Ht.Optional)?.setUpPreloading(),t.get(ii,null,Ht.Optional)?.init(),r.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var Bo=new N("",{factory:()=>new G}),qo=new N("",{providedIn:"root",factory:()=>1});var Vo=new N("");var oi=[{path:"",redirectTo:"/home",pathMatch:"full"},{path:"home",loadComponent:()=>import("./chunk-OQ3VGZW2.js").then(t=>t.HomeComponent)},{path:"features",loadComponent:()=>import("./chunk-HOZEH2OY.js").then(t=>t.FeaturesComponent)},{path:"company",loadComponent:()=>import("./chunk-CNYUX7LB.js").then(t=>t.CompanyComponent)},{path:"about",loadComponent:()=>import("./chunk-6SR6BQ2C.js").then(t=>t.AboutComponent)},{path:"resources",loadComponent:()=>import("./chunk-UVZXZRZJ.js").then(t=>t.ResourcesComponent)},{path:"contact",loadComponent:()=>import("./chunk-BHMMDLQA.js").then(t=>t.ContactComponent)},{path:"demo",loadComponent:()=>import("./chunk-U7JUUIMJ.js").then(t=>t.DemoComponent)},{path:"**",redirectTo:"/home"}];var si={providers:[cr({eventCoalescing:!0}),xn(oi,In({scrollPositionRestoration:"top"}))]};var _t=class t{static \u0275fac=function(e){return new(e||t)};static \u0275cmp=F({type:t,selectors:[["app-header"]],decls:32,vars:0,consts:[[1,"bg-white/95","backdrop-blur-md","shadow-lg","border-b","border-neutral-200","sticky","top-0","z-50"],[1,"container-custom"],[1,"flex","justify-between","items-center","py-4"],[1,"flex","items-center","space-x-3"],[1,"w-12","h-12","gradient-primary","rounded-xl","flex","items-center","justify-center","shadow-primary","hover-scale"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"],[1,"text-xl","font-bold","text-neutral-900"],[1,"text-xs","text-neutral-500"],[1,"hidden","lg:flex","items-center","space-x-8"],[1,"flex","items-center","space-x-6"],["routerLink","/home","routerLinkActive","active",1,"nav-link"],["routerLink","/features","routerLinkActive","active",1,"nav-link"],["routerLink","/company","routerLinkActive","active",1,"nav-link"],["routerLink","/about","routerLinkActive","active",1,"nav-link"],["routerLink","/resources","routerLinkActive","active",1,"nav-link"],[1,"hidden","md:flex","items-center","space-x-3"],["routerLink","/contact",1,"btn","btn-ghost","btn-sm"],["routerLink","/demo",1,"btn","btn-outline","btn-sm"],[1,"md:hidden","p-2","rounded-lg","hover:bg-neutral-100","transition-colors"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-neutral-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 6h16M4 12h16M4 18h16"]],template:function(e,r){e&1&&(u(0,"header",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4),se(),u(5,"svg",5),j(6,"path",6),h()(),rt(),u(7,"div")(8,"h1",7),m(9,"AI Hub"),h(),u(10,"p",8),m(11,"Intelligent Workspace"),h()()(),u(12,"nav",9)(13,"div",10)(14,"a",11),m(15,"Home"),h(),u(16,"a",12),m(17,"Features"),h(),u(18,"a",13),m(19,"Company"),h(),u(20,"a",14),m(21,"About"),h(),u(22,"a",15),m(23,"Resources"),h()()(),u(24,"div",16)(25,"a",17),m(26,"Contact Sales"),h(),u(27,"a",18),m(28,"Watch Demo"),h()(),u(29,"button",19),se(),u(30,"svg",20),j(31,"path",21),h()()()()())},dependencies:[Oe,bn],encapsulation:2})};var Ut=class t{static \u0275fac=function(e){return new(e||t)};static \u0275cmp=F({type:t,selectors:[["app-footer"]],decls:94,vars:0,consts:[[1,"bg-neutral-900","text-white","py-12"],[1,"container-custom"],[1,"grid","md:grid-cols-4","gap-8"],[1,"md:col-span-1"],[1,"flex","items-center","space-x-3","mb-4"],[1,"w-10","h-10","gradient-primary","rounded-lg","flex","items-center","justify-center"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"],[1,"text-lg","font-bold","text-white"],[1,"text-xs","text-white"],[1,"text-white","text-sm","mb-4"],[1,"border-t","border-neutral-700","pt-4"],[1,"text-gray-300","text-xs","font-medium","mb-2"],[1,"flex","items-center","space-x-3"],[1,"w-8","h-8","bg-gradient-to-r","from-blue-500","to-purple-600","rounded-lg","flex","items-center","justify-center"],[1,"text-white","font-bold","text-xs"],[1,"text-white","font-semibold","text-sm"],[1,"text-gray-300","text-xs"],[1,"text-gray-300","text-xs","mt-2"],["href","https://izontechsolution.com/","target","_blank","rel","noopener noreferrer",1,"text-blue-400","hover:text-blue-300","text-xs","transition-colors","inline-block","mt-1"],[1,"font-semibold","mb-4","text-white"],[1,"space-y-2","text-sm","text-white"],["href","#features",1,"text-white","hover:text-gray-300","transition-colors"],["href","#pricing",1,"text-white","hover:text-gray-300","transition-colors"],["href","#integrations",1,"text-white","hover:text-gray-300","transition-colors"],["href","#api",1,"text-white","hover:text-gray-300","transition-colors"],["href","#about",1,"text-white","hover:text-gray-300","transition-colors"],["href","#careers",1,"text-white","hover:text-gray-300","transition-colors"],["href","#blog",1,"text-white","hover:text-gray-300","transition-colors"],["href","#contact",1,"text-white","hover:text-gray-300","transition-colors"],["href","#help",1,"text-white","hover:text-gray-300","transition-colors"],["href","#docs",1,"text-white","hover:text-gray-300","transition-colors"],["href","#community",1,"text-white","hover:text-gray-300","transition-colors"],["href","#status",1,"text-white","hover:text-gray-300","transition-colors"],[1,"border-t","border-neutral-800","mt-8","pt-8","flex","flex-col","md:flex-row","justify-between","items-center"],[1,"text-center","md:text-left"],[1,"text-white","text-sm"],[1,"text-gray-300","text-xs","mt-1"],["href","https://izontechsolution.com/","target","_blank","rel","noopener noreferrer",1,"text-blue-400","hover:text-blue-300","transition-colors"],[1,"flex","space-x-6","mt-4","md:mt-0"],["href","#privacy",1,"text-white","hover:text-gray-300","text-sm","transition-colors"],["href","#terms",1,"text-white","hover:text-gray-300","text-sm","transition-colors"],["href","#security",1,"text-white","hover:text-gray-300","text-sm","transition-colors"]],template:function(e,r){e&1&&(u(0,"footer",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5),se(),u(6,"svg",6),j(7,"path",7),h()(),rt(),u(8,"div")(9,"h3",8),m(10,"AI Hub"),h(),u(11,"p",9),m(12,"Intelligent Workspace"),h()()(),u(13,"p",10),m(14," Transform your workflow with AI-powered productivity tools. "),h(),u(15,"div",11)(16,"p",12),m(17,"Powered by"),h(),u(18,"div",13)(19,"div",14)(20,"span",15),m(21,"IT"),h()(),u(22,"div")(23,"h4",16),m(24,"IzonTech Solutions"),h(),u(25,"p",17),m(26,"Technology Innovation Company"),h()()(),u(27,"p",18),m(28," Building next-generation AI solutions for modern businesses "),h(),u(29,"a",19),m(30," Visit IzonTech Solutions \u2192 "),h()()(),u(31,"div")(32,"h4",20),m(33,"Product"),h(),u(34,"ul",21)(35,"li")(36,"a",22),m(37,"Features"),h()(),u(38,"li")(39,"a",23),m(40,"Pricing"),h()(),u(41,"li")(42,"a",24),m(43,"Integrations"),h()(),u(44,"li")(45,"a",25),m(46,"API"),h()()()(),u(47,"div")(48,"h4",20),m(49,"Company"),h(),u(50,"ul",21)(51,"li")(52,"a",26),m(53,"About"),h()(),u(54,"li")(55,"a",27),m(56,"Careers"),h()(),u(57,"li")(58,"a",28),m(59,"Blog"),h()(),u(60,"li")(61,"a",29),m(62,"Contact"),h()()()(),u(63,"div")(64,"h4",20),m(65,"Support"),h(),u(66,"ul",21)(67,"li")(68,"a",30),m(69,"Help Center"),h()(),u(70,"li")(71,"a",31),m(72,"Documentation"),h()(),u(73,"li")(74,"a",32),m(75,"Community"),h()(),u(76,"li")(77,"a",33),m(78,"Status"),h()()()()(),u(79,"div",34)(80,"div",35)(81,"p",36),m(82," \xA9 2024 AI Hub by IzonTech Solutions. All rights reserved. "),h(),u(83,"p",37),m(84," Developed and maintained by "),u(85,"a",38),m(86,"IzonTech Solutions"),h()()(),u(87,"div",39)(88,"a",40),m(89,"Privacy"),h(),u(90,"a",41),m(91,"Terms"),h(),u(92,"a",42),m(93,"Security"),h()()()()())},styles:["footer[_ngcontent-%COMP%]{background-color:#171717!important;color:#fff!important}footer[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#fff!important;font-weight:700}footer[_ngcontent-%COMP%]   .text-neutral-400[_ngcontent-%COMP%]{color:#fff!important}footer[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#fff!important;font-weight:600}footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#fff!important}footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff!important;text-decoration:none;transition:color .2s ease}footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#e5e5e5!important}footer[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{color:#fff!important}footer[_ngcontent-%COMP%]   .border-t[_ngcontent-%COMP%]{border-color:#404040!important}footer[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{color:#fff!important}footer[_ngcontent-%COMP%]   .text-sm[_ngcontent-%COMP%], footer[_ngcontent-%COMP%]   .text-xs[_ngcontent-%COMP%], footer[_ngcontent-%COMP%]   .text-lg[_ngcontent-%COMP%]{color:#fff!important}footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#d1d5db!important;opacity:.8}"]})};var Ee=class t{constructor(n){this.router=n;this.router.events.pipe(Y(e=>e instanceof M)).subscribe(()=>{this.scrollToTop()})}scrollToTop(){typeof window<"u"&&window.scrollTo({top:0,left:0,behavior:"smooth"})}scrollToTopInstant(){typeof window<"u"&&window.scrollTo(0,0)}scrollToElement(n){if(typeof document<"u"){let e=document.getElementById(n);e&&e.scrollIntoView({behavior:"smooth",block:"start"})}}scrollToPosition(n,e=0){typeof window<"u"&&window.scrollTo({top:n,left:e,behavior:"smooth"})}getCurrentScrollPosition(){return typeof window<"u"?{x:window.pageXOffset||document.documentElement.scrollLeft,y:window.pageYOffset||document.documentElement.scrollTop}:{x:0,y:0}}hasScrolledPast(n){return this.getCurrentScrollPosition().y>n}static \u0275fac=function(e){return new(e||t)(fe(ie))};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})};function Wo(t,n){if(t&1){let e=rr();u(0,"button",1),ke("click",function(){Ln(e);let i=ir();return $n(i.scrollToTop())}),se(),u(1,"svg",2),j(2,"path",3),h()()}}var kt=class t{constructor(n){this.scrollService=n}showScrollButton=!1;onWindowScroll(){this.showScrollButton=this.scrollService.hasScrolledPast(300)}scrollToTop(){this.scrollService.scrollToTop()}static \u0275fac=function(e){return new(e||t)(T(Ee))};static \u0275cmp=F({type:t,selectors:[["app-scroll-to-top"]],hostBindings:function(e,r){e&1&&ke("scroll",function(){return r.onWindowScroll()},!1,Wn)},decls:1,vars:1,consts:[["class","fixed bottom-8 right-8 z-40 w-12 h-12 bg-primary-500 hover:bg-primary-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group","aria-label","Scroll to top",3,"click",4,"ngIf"],["aria-label","Scroll to top",1,"fixed","bottom-8","right-8","z-40","w-12","h-12","bg-primary-500","hover:bg-primary-600","text-white","rounded-full","shadow-lg","hover:shadow-xl","transition-all","duration-300","flex","items-center","justify-center","group",3,"click"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","transition-transform","group-hover:-translate-y-1"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M5 10l7-7m0 0l7 7m-7-7v18"]],template:function(e,r){e&1&&Zn(0,Wo,3,0,"button",0),e&2&&nr("ngIf",r.showScrollButton)},dependencies:[dr,ur],styles:["button[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .3s ease-out}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}button[_ngcontent-%COMP%]:hover{animation:_ngcontent-%COMP%_pulse 1s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #2a2e5bb3}70%{box-shadow:0 0 0 10px #2a2e5b00}to{box-shadow:0 0 #2a2e5b00}}"]})};var Nt=class t{constructor(n){this.scrollService=n}title="WebApp";static \u0275fac=function(e){return new(e||t)(T(Ee))};static \u0275cmp=F({type:t,selectors:[["app-root"]],decls:4,vars:0,template:function(e,r){e&1&&j(0,"app-header")(1,"router-outlet")(2,"app-footer")(3,"app-scroll-to-top")},dependencies:[Ke,_t,Ut,kt],encapsulation:2})};pr(Nt,si).catch(t=>console.error(t));
