<!-- About Section -->
<section id="about" class="py-20 bg-white">
  <div class="container-custom">
    <!-- Hero Introduction -->
    <div class="text-center mb-16">
      <div class="inline-flex items-center px-4 py-2 bg-primary-100 rounded-full text-primary-700 text-sm font-medium mb-6">
        <div class="w-6 h-6 gradient-primary rounded-full flex items-center justify-center mr-2">
          <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        About AI Hub
      </div>
      <h2 class="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
        Revolutionizing Workspace <span class="text-primary-500">Intelligence</span>
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
        AI Hub is more than just a productivity platform—it's a vision realized. Born from the need to bridge
        the gap between human creativity and artificial intelligence, we're building the future of work.
      </p>
    </div>

    <!-- Mission & Vision -->
    <div class="grid lg:grid-cols-2 gap-12 mb-20">
      <!-- Mission -->
      <div class="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-2xl p-8">
        <div class="w-16 h-16 gradient-primary rounded-2xl flex items-center justify-center mb-6">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-neutral-900 mb-4">Our Mission</h3>
        <p class="text-neutral-700 leading-relaxed mb-6">
          To democratize artificial intelligence and make advanced AI tools accessible to every professional,
          team, and organization. We believe that AI should amplify human potential, not replace it.
        </p>
        <div class="space-y-3">
          <div class="flex items-center text-neutral-700">
            <svg class="w-5 h-5 text-primary-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Empower teams with intelligent automation
          </div>
          <div class="flex items-center text-neutral-700">
            <svg class="w-5 h-5 text-primary-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Simplify complex workflows
          </div>
          <div class="flex items-center text-neutral-700">
            <svg class="w-5 h-5 text-primary-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Foster innovation and creativity
          </div>
        </div>
      </div>

      <!-- Vision -->
      <div class="bg-gradient-to-br from-secondary-50 to-accent-50 rounded-2xl p-8">
        <div class="w-16 h-16 bg-secondary-500 rounded-2xl flex items-center justify-center mb-6">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-neutral-900 mb-4">Our Vision</h3>
        <p class="text-neutral-700 leading-relaxed mb-6">
          To create a world where every workspace is intelligent, every task is optimized, and every team
          achieves their highest potential through seamless human-AI collaboration.
        </p>
        <div class="space-y-3">
          <div class="flex items-center text-neutral-700">
            <svg class="w-5 h-5 text-secondary-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Global AI-powered workspace standard
          </div>
          <div class="flex items-center text-neutral-700">
            <svg class="w-5 h-5 text-secondary-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Seamless human-AI collaboration
          </div>
          <div class="flex items-center text-neutral-700">
            <svg class="w-5 h-5 text-secondary-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Unlimited productivity potential
          </div>
        </div>
      </div>
    </div>

    <!-- Our Story -->
    <div class="mb-20">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-neutral-900 mb-4">Our Story</h3>
        <p class="text-xl text-neutral-600 max-w-2xl mx-auto">
          The journey from concept to the AI-powered workspace revolution
        </p>
      </div>

      <div class="relative">
        <!-- Timeline -->
        <div class="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-primary-200 to-secondary-200 rounded-full"></div>

        <div class="space-y-12">
          <!-- Story Point 1 -->
          <div class="flex items-center">
            <div class="w-1/2 pr-8 text-right">
              <div class="bg-white rounded-xl p-6 shadow-lg border border-neutral-100">
                <h4 class="text-lg font-semibold text-neutral-900 mb-2">The Vision (2023)</h4>
                <p class="text-neutral-600 text-sm">
                  IzonTech Solutions identified the growing need for intelligent workspace management.
                  Teams were struggling with fragmented tools and inefficient workflows.
                </p>
              </div>
            </div>
            <div class="w-12 h-12 gradient-primary rounded-full flex items-center justify-center relative z-10">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            </div>
            <div class="w-1/2 pl-8">
              <div class="bg-primary-50 rounded-xl p-6">
                <div class="text-primary-600 font-medium text-sm mb-1">Research & Planning</div>
                <p class="text-neutral-700 text-sm">
                  Extensive market research and user interviews revealed the pain points in modern workspace management.
                </p>
              </div>
            </div>
          </div>

          <!-- Story Point 2 -->
          <div class="flex items-center">
            <div class="w-1/2 pr-8 text-right">
              <div class="bg-secondary-50 rounded-xl p-6">
                <div class="text-secondary-600 font-medium text-sm mb-1">Development Phase</div>
                <p class="text-neutral-700 text-sm">
                  Our expert team began building the core AI engine and user interface with cutting-edge technologies.
                </p>
              </div>
            </div>
            <div class="w-12 h-12 bg-secondary-500 rounded-full flex items-center justify-center relative z-10">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
              </svg>
            </div>
            <div class="w-1/2 pl-8">
              <div class="bg-white rounded-xl p-6 shadow-lg border border-neutral-100">
                <h4 class="text-lg font-semibold text-neutral-900 mb-2">Development (2024)</h4>
                <p class="text-neutral-600 text-sm">
                  Leveraging IzonTech's 15+ years of experience, we built AI Hub using the latest
                  AI/ML technologies, cloud infrastructure, and modern web frameworks.
                </p>
              </div>
            </div>
          </div>

          <!-- Story Point 3 -->
          <div class="flex items-center">
            <div class="w-1/2 pr-8 text-right">
              <div class="bg-white rounded-xl p-6 shadow-lg border border-neutral-100">
                <h4 class="text-lg font-semibold text-neutral-900 mb-2">Launch & Growth (2024)</h4>
                <p class="text-neutral-600 text-sm">
                  AI Hub launched with revolutionary features that immediately transformed how teams
                  collaborate and manage their workflows.
                </p>
              </div>
            </div>
            <div class="w-12 h-12 bg-accent-400 rounded-full flex items-center justify-center relative z-10">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <div class="w-1/2 pl-8">
              <div class="bg-accent-50 rounded-xl p-6">
                <div class="text-accent-600 font-medium text-sm mb-1">Market Success</div>
                <p class="text-neutral-700 text-sm">
                  Rapid adoption by teams worldwide, with users reporting 300% productivity improvements.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Core Values -->
    <div class="mb-20">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-neutral-900 mb-4">Our Core Values</h3>
        <p class="text-xl text-neutral-600 max-w-2xl mx-auto">
          The principles that guide everything we do at AI Hub
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Innovation -->
        <div class="text-center group">
          <div class="w-20 h-20 gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-3">Innovation</h4>
          <p class="text-neutral-600 text-sm leading-relaxed">
            We constantly push boundaries, exploring new technologies and methodologies to deliver cutting-edge solutions.
          </p>
        </div>

        <!-- Collaboration -->
        <div class="text-center group">
          <div class="w-20 h-20 bg-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-3">Collaboration</h4>
          <p class="text-neutral-600 text-sm leading-relaxed">
            We believe in the power of teamwork, both within our organization and with our users and partners.
          </p>
        </div>

        <!-- Excellence -->
        <div class="text-center group">
          <div class="w-20 h-20 bg-accent-400 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-3">Excellence</h4>
          <p class="text-neutral-600 text-sm leading-relaxed">
            We strive for perfection in every detail, ensuring our platform meets the highest standards of quality.
          </p>
        </div>

        <!-- Empowerment -->
        <div class="text-center group">
          <div class="w-20 h-20 bg-warning-400 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-3">Empowerment</h4>
          <p class="text-neutral-600 text-sm leading-relaxed">
            We empower individuals and teams to achieve their full potential through intelligent technology solutions.
          </p>
        </div>
      </div>
    </div>

    <!-- Team Section -->
    <div class="mb-20">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-neutral-900 mb-4">Meet Our Team</h3>
        <p class="text-xl text-neutral-600 max-w-2xl mx-auto">
          The brilliant minds behind AI Hub's revolutionary platform
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Team Member 1 -->
        <div class="bg-white rounded-2xl p-6 shadow-lg border border-neutral-100 text-center hover:shadow-xl transition-shadow duration-300">
          <div class="w-24 h-24 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl font-bold text-white">AK</span>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-2">Alex Kumar</h4>
          <p class="text-primary-600 font-medium text-sm mb-3">Lead AI Engineer</p>
          <p class="text-neutral-600 text-sm leading-relaxed mb-4">
            10+ years in AI/ML development. Previously at Google and Microsoft, specializing in natural language processing and machine learning algorithms.
          </p>
          <div class="flex justify-center space-x-3">
            <a href="#" class="w-8 h-8 bg-neutral-100 rounded-full flex items-center justify-center hover:bg-primary-100 transition-colors">
              <svg class="w-4 h-4 text-neutral-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
            <a href="#" class="w-8 h-8 bg-neutral-100 rounded-full flex items-center justify-center hover:bg-primary-100 transition-colors">
              <svg class="w-4 h-4 text-neutral-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Team Member 2 -->
        <div class="bg-white rounded-2xl p-6 shadow-lg border border-neutral-100 text-center hover:shadow-xl transition-shadow duration-300">
          <div class="w-24 h-24 bg-secondary-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl font-bold text-white">SP</span>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-2">Sarah Parker</h4>
          <p class="text-secondary-600 font-medium text-sm mb-3">UX/UI Design Lead</p>
          <p class="text-neutral-600 text-sm leading-relaxed mb-4">
            8+ years in product design. Former design lead at Airbnb and Spotify, passionate about creating intuitive and beautiful user experiences.
          </p>
          <div class="flex justify-center space-x-3">
            <a href="#" class="w-8 h-8 bg-neutral-100 rounded-full flex items-center justify-center hover:bg-secondary-100 transition-colors">
              <svg class="w-4 h-4 text-neutral-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
            <a href="#" class="w-8 h-8 bg-neutral-100 rounded-full flex items-center justify-center hover:bg-secondary-100 transition-colors">
              <svg class="w-4 h-4 text-neutral-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Team Member 3 -->
        <div class="bg-white rounded-2xl p-6 shadow-lg border border-neutral-100 text-center hover:shadow-xl transition-shadow duration-300">
          <div class="w-24 h-24 bg-accent-400 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl font-bold text-white">MR</span>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-2">Michael Rodriguez</h4>
          <p class="text-accent-600 font-medium text-sm mb-3">Full-Stack Developer</p>
          <p class="text-neutral-600 text-sm leading-relaxed mb-4">
            12+ years in software development. Expert in Angular, Node.js, and cloud technologies. Previously at Amazon and Tesla.
          </p>
          <div class="flex justify-center space-x-3">
            <a href="#" class="w-8 h-8 bg-neutral-100 rounded-full flex items-center justify-center hover:bg-accent-100 transition-colors">
              <svg class="w-4 h-4 text-neutral-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
            <a href="#" class="w-8 h-8 bg-neutral-100 rounded-full flex items-center justify-center hover:bg-accent-100 transition-colors">
              <svg class="w-4 h-4 text-neutral-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- User Testimonials -->
    <div class="mb-20">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-neutral-900 mb-4">What Our Users Say</h3>
        <p class="text-xl text-neutral-600 max-w-2xl mx-auto">
          Real feedback from teams who've transformed their workflows with AI Hub
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Testimonial 1 -->
        <div class="bg-gradient-to-br from-primary-50 to-white rounded-2xl p-6 border border-primary-100">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 gradient-primary rounded-full flex items-center justify-center mr-4">
              <span class="text-white font-bold text-sm">JD</span>
            </div>
            <div>
              <h5 class="font-semibold text-neutral-900">Jennifer Davis</h5>
              <p class="text-neutral-600 text-sm">Product Manager, TechCorp</p>
            </div>
          </div>
          <div class="flex mb-3">
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" *ngFor="let star of [1,2,3,4,5]">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          </div>
          <p class="text-neutral-700 text-sm leading-relaxed">
            "AI Hub has completely revolutionized how our team manages projects. The AI-powered insights
            have helped us identify bottlenecks we never knew existed. Our productivity has increased by 250%!"
          </p>
        </div>

        <!-- Testimonial 2 -->
        <div class="bg-gradient-to-br from-secondary-50 to-white rounded-2xl p-6 border border-secondary-100">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-secondary-500 rounded-full flex items-center justify-center mr-4">
              <span class="text-white font-bold text-sm">RC</span>
            </div>
            <div>
              <h5 class="font-semibold text-neutral-900">Robert Chen</h5>
              <p class="text-neutral-600 text-sm">CTO, StartupXYZ</p>
            </div>
          </div>
          <div class="flex mb-3">
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" *ngFor="let star of [1,2,3,4,5]">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          </div>
          <p class="text-neutral-700 text-sm leading-relaxed">
            "The intelligent automation features are incredible. Tasks that used to take hours now complete
            in minutes. AI Hub has become an essential part of our development workflow."
          </p>
        </div>

        <!-- Testimonial 3 -->
        <div class="bg-gradient-to-br from-accent-50 to-white rounded-2xl p-6 border border-accent-100">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-accent-400 rounded-full flex items-center justify-center mr-4">
              <span class="text-white font-bold text-sm">LM</span>
            </div>
            <div>
              <h5 class="font-semibold text-neutral-900">Lisa Martinez</h5>
              <p class="text-neutral-600 text-sm">Operations Director, GlobalTech</p>
            </div>
          </div>
          <div class="flex mb-3">
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" *ngFor="let star of [1,2,3,4,5]">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          </div>
          <p class="text-neutral-700 text-sm leading-relaxed">
            "AI Hub's collaborative features have transformed how our remote team works together.
            The real-time insights and smart recommendations have made us more efficient than ever."
          </p>
        </div>
      </div>
    </div>

    <!-- Statistics -->
    <div class="bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 mb-20 text-white">
      <div class="text-center mb-8">
        <h3 class="text-3xl font-bold mb-4">AI Hub by the Numbers</h3>
        <p class="text-white/90 text-lg">
          The impact we've made since launch
        </p>
      </div>

      <div class="grid md:grid-cols-4 gap-8 text-center">
        <div>
          <div class="text-4xl font-bold mb-2">10K+</div>
          <div class="text-white/80 text-sm">Active Users</div>
        </div>
        <div>
          <div class="text-4xl font-bold mb-2">50K+</div>
          <div class="text-white/80 text-sm">Projects Managed</div>
        </div>
        <div>
          <div class="text-4xl font-bold mb-2">300%</div>
          <div class="text-white/80 text-sm">Avg. Productivity Boost</div>
        </div>
        <div>
          <div class="text-4xl font-bold mb-2">99.9%</div>
          <div class="text-white/80 text-sm">Uptime Reliability</div>
        </div>
      </div>
    </div>

    <!-- Call to Action -->
    <div class="text-center">
      <div class="max-w-3xl mx-auto">
        <h3 class="text-3xl font-bold text-neutral-900 mb-6">
          Ready to Transform Your Workflow?
        </h3>
        <p class="text-xl text-neutral-600 mb-8 leading-relaxed">
          Join thousands of teams who have already revolutionized their productivity with AI Hub.
          Experience the future of intelligent workspace management today.
        </p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
          <a href="#pricing" class="btn btn-primary btn-lg">
            Start Free Trial
          </a>
          <a href="#features" class="btn btn-outline btn-lg">
            Explore Features
          </a>
        </div>

        <div class="flex items-center justify-center space-x-6 text-sm text-neutral-500">
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            No credit card required
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            14-day free trial
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Cancel anytime
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
