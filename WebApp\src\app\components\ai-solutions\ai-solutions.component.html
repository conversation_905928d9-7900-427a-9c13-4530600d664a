<!-- AI Solutions Section -->
<section class="py-20 bg-white">
  <div class="container-custom">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
        AI-Powered Solutions
      </h2>
      <p class="text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
        Transform your business operations with our comprehensive suite of AI capabilities designed for modern teams and enterprises. Unlock unprecedented efficiency, innovation, and growth.
      </p>
    </div>

    <!-- Solutions Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div *ngFor="let solution of solutions"
           class="group card p-8 bg-white border border-neutral-200 hover-lift cursor-pointer transition-all duration-300"
           (click)="onSolutionClick(solution)">

        <!-- Icon -->
        <div class="mb-6">
          <div class="w-16 h-16 rounded-2xl flex items-center justify-center transition-all duration-300"
               [ngClass]="getColorClasses(solution.color)">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="getIconPath(solution.icon)"></path>
            </svg>
          </div>
        </div>

        <!-- Content -->
        <div class="mb-6">
          <h3 class="text-xl font-bold text-neutral-900 mb-3 group-hover:text-primary-600 transition-colors">
            {{ solution.title }}
          </h3>
          <p class="text-neutral-600 leading-relaxed mb-4">
            {{ solution.description }}
          </p>
        </div>

        <!-- Features List -->
        <div class="space-y-2">
          <div *ngFor="let feature of solution.features" class="flex items-center text-sm text-neutral-500">
            <svg class="w-4 h-4 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            {{ feature }}
          </div>
        </div>

        <!-- Hover Arrow -->
        <div class="mt-6 flex items-center text-primary-500 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-0 group-hover:translate-x-2">
          <span class="text-sm font-medium mr-2">Learn More</span>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- Bottom CTA -->
    <div class="text-center mt-16">
      <p class="text-lg text-neutral-600 mb-6">
        Ready to transform your business with AI? Let's explore what's possible together.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="/demo" class="btn btn-primary btn-lg text-white" >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0 9 9 0 01-18 0z"></path>
          </svg>
          Watch Demo
        </a>
        <a href="/contact" class="btn btn-outline btn-lg">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
          Contact Sales
        </a>
      </div>
    </div>
  </div>
</section>
