<!-- Core Features Section -->
<section id="features" class="section-padding bg-white">
  <div class="container-custom">
    <!-- Section Header -->
    <div class="text-center mb-20">
      <div class="inline-flex items-center px-4 py-2 bg-primary-50 rounded-full text-sm font-medium text-primary-600 mb-6">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
        Powerful Features
      </div>
      <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-900 mb-6">
        Everything You Need for
        <span class="text-gradient">AI-Powered Productivity</span>
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
        Discover how AI Hub transforms your workflow with intelligent automation, seamless collaboration, and powerful analytics.
      </p>
    </div>

    <!-- Features Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- AI Chat & Agents -->
      <div class="card p-6 bg-white hover-lift border border-neutral-200">
        <div class="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center shadow-primary mb-4">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-neutral-900 mb-3">AI Chat & Agents</h3>
        <p class="text-neutral-600 mb-4">Intelligent conversations with AI agents that understand context and provide real-time assistance.</p>
        <ul class="space-y-2">
          <li class="flex items-center text-sm text-neutral-600">
            <svg class="w-4 h-4 text-accent-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Real-time responses
          </li>
          <li class="flex items-center text-sm text-neutral-600">
            <svg class="w-4 h-4 text-accent-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Context awareness
          </li>
        </ul>
      </div>

      <!-- Task Management -->
      <div class="card p-6 bg-white hover-lift border border-neutral-200">
        <div class="w-12 h-12 bg-accent-400 rounded-xl flex items-center justify-center shadow-lg mb-4">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-neutral-900 mb-3">Task Management</h3>
        <p class="text-neutral-600 mb-4">Organize projects with intelligent task automation and collaborative workflows.</p>
        <ul class="space-y-2">
          <li class="flex items-center text-sm text-neutral-600">
            <svg class="w-4 h-4 text-accent-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Kanban boards
          </li>
          <li class="flex items-center text-sm text-neutral-600">
            <svg class="w-4 h-4 text-accent-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Smart automation
          </li>
        </ul>
      </div>

      <!-- Analytics -->
      <div class="card p-6 bg-white hover-lift border border-neutral-200">
        <div class="w-12 h-12 bg-secondary-500 rounded-xl flex items-center justify-center shadow-lg mb-4">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-neutral-900 mb-3">Real-time Analytics</h3>
        <p class="text-neutral-600 mb-4">Track performance with comprehensive analytics and intelligent insights.</p>
        <ul class="space-y-2">
          <li class="flex items-center text-sm text-neutral-600">
            <svg class="w-4 h-4 text-accent-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Live dashboards
          </li>
          <li class="flex items-center text-sm text-neutral-600">
            <svg class="w-4 h-4 text-accent-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Performance metrics
          </li>
        </ul>
      </div>
    </div>
  </div>
</section>
