/* Company Showcase Styles */

/* Hover effects for cards */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}

/* Animation for stats */
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-item {
  animation: countUp 0.6s ease-out;
}

/* Service cards */
.service-card {
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.service-card:hover {
  border-color: #3b82f6;
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

/* Portfolio cards */
.portfolio-card {
  transition: all 0.3s ease;
  overflow: hidden;
}

.portfolio-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
}

/* Technology tags */
.tech-tag {
  transition: all 0.2s ease;
}

.tech-tag:hover {
  transform: scale(1.05);
}

/* Company info card */
.company-info-card {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  position: relative;
  overflow: hidden;
}

.company-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hover-lift:hover {
    transform: translateY(-4px);
  }

  .portfolio-card:hover {
    transform: translateY(-3px);
  }
}

/* Loading animation */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Button hover effects */
.btn-company {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-company::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-company:hover::before {
  left: 100%;
}
