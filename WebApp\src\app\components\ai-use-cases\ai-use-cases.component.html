<!-- AI Use Cases Section -->
<section class="py-20 bg-neutral-50">
  <div class="container-custom">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
        Top AI Use Cases for Teams
      </h2>
      <p class="text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
        Discover how AI Hub transforms the way teams work, collaborate, and achieve their goals. From boosting productivity to scaling operations, explore the possibilities.
      </p>
    </div>

    <!-- Accordion Container -->
    <div class="max-w-4xl mx-auto space-y-4">
      <div *ngFor="let useCase of useCases" class="accordion-item">
        <!-- Accordion Header -->
        <button 
          (click)="toggleAccordion(useCase.id)"
          class="w-full flex items-center justify-between p-6 bg-white border border-neutral-200 rounded-lg hover:bg-neutral-50 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          [class.rounded-b-none]="isActive(useCase.id)"
          [class.border-b-0]="isActive(useCase.id)">
          
          <div class="flex items-center">
            <!-- Icon -->
            <div class="w-12 h-12 rounded-xl flex items-center justify-center mr-4 transition-all duration-300"
                 [ngClass]="getColorClasses(useCase.color)">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="getIconPath(useCase.icon)"></path>
              </svg>
            </div>
            
            <!-- Title and Description -->
            <div class="text-left">
              <h3 class="text-xl font-bold text-neutral-900 mb-1">{{ useCase.title }}</h3>
              <p class="text-neutral-600 text-sm">{{ useCase.description }}</p>
            </div>
          </div>

          <!-- Chevron Icon -->
          <svg 
            class="w-6 h-6 text-neutral-400 transition-transform duration-300"
            [class.rotate-180]="isActive(useCase.id)"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>

        <!-- Accordion Content -->
        <div 
          class="accordion-content overflow-hidden transition-all duration-300"
          [class.max-h-0]="!isActive(useCase.id)"
          [class.max-h-screen]="isActive(useCase.id)">
          
          <div class="bg-white border border-neutral-200 border-t-0 rounded-b-lg p-6">
            <div class="grid md:grid-cols-2 gap-6">
              <div *ngFor="let item of useCase.items" class="space-y-3">
                <div class="flex items-start">
                  <div class="w-2 h-2 rounded-full bg-primary-500 mt-2 mr-3 flex-shrink-0"></div>
                  <div>
                    <h4 class="font-semibold text-neutral-900 mb-2">{{ item.title }}</h4>
                    <p class="text-neutral-600 text-sm leading-relaxed">{{ item.description }}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- CTA in accordion -->
            <div class="mt-8 pt-6 border-t border-neutral-100">
              <div class="flex flex-col sm:flex-row gap-3">
                <a href="/demo" class="btn btn-primary btn-sm">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0 9 9 0 01-18 0z"></path>
                  </svg>
                  See Demo
                </a>
                <a href="/features" class="btn btn-ghost btn-sm">
                  Learn More
                  <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Stats -->
    <div class="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
      <div class="space-y-2">
        <div class="text-3xl font-bold text-primary-600">40%</div>
        <div class="text-sm text-neutral-600">Productivity Increase</div>
      </div>
      <div class="space-y-2">
        <div class="text-3xl font-bold text-secondary-600">60%</div>
        <div class="text-sm text-neutral-600">Time Saved</div>
      </div>
      <div class="space-y-2">
        <div class="text-3xl font-bold text-accent-600">25%</div>
        <div class="text-sm text-neutral-600">Cost Reduction</div>
      </div>
      <div class="space-y-2">
        <div class="text-3xl font-bold text-warning-600">99%</div>
        <div class="text-sm text-neutral-600">Uptime Guarantee</div>
      </div>
    </div>
  </div>
</section>
