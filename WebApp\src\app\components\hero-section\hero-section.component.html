<!-- Hero Section -->
<section id="home" class="relative overflow-hidden">
  <!-- Background with animated gradient -->
  <div class="absolute inset-0 gradient-hero"></div>
  <div class="absolute inset-0 bg-black/10"></div>

  <!-- Animated background elements -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
    <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-accent-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-secondary-500/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 4s;"></div>
  </div>

  <div class="relative container-custom section-padding">
    <div class="max-w-6xl mx-auto">
      <div class="grid lg:grid-cols-2 gap-12 items-center">
        <!-- Left Column - Content -->
        <div class="text-white animate-fade-in">
          <!-- Company Badge -->
          <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-sm font-medium text-white/90 mb-4">
            <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-2">
              <span class="text-white font-bold text-xs">IT</span>
            </div>
            Powered by IzonTech Solutions
          </div>

          <!-- Product Badge -->
          <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-sm font-medium text-white/90 mb-6">
            <svg class="w-4 h-4 mr-2 text-accent-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            New: AI Agents 2.0 Now Available
          </div>

          <!-- Main Headline -->
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-6 leading-tight">
            <span class="block text-white">Transform Your</span>
            <span class="block text-accent-400">Workflow with AI</span>
            <span class="block text-white">Workspace Management</span>
          </h1>

          <!-- Subheadline -->
          <p class="text-xl md:text-2xl text-white/90 mb-6 leading-relaxed max-w-2xl">
            Streamline projects, collaborate with intelligent AI agents, and manage tasks in one powerful platform.
            <span class="text-accent-400 font-semibold">Boost productivity by 300%</span> with our AI-powered workspace.
          </p>

          <!-- Company Info -->
          <div class="bg-white/5 backdrop-blur-sm rounded-lg p-4 mb-8 max-w-2xl">
            <p class="text-white/80 text-sm leading-relaxed">
              <span class="text-accent-400 font-semibold">IzonTech Solutions</span> - A leading technology innovation company with
              <span class="text-white font-medium">15+ years of experience</span> in developing cutting-edge software solutions.
              We specialize in AI/ML, cloud computing, enterprise software, and digital transformation services for businesses worldwide.
            </p>
          </div>

          <!-- Key Benefits -->
          <div class="grid sm:grid-cols-2 gap-4 mb-8">
            <div class="flex items-center text-white/90">
              <svg class="w-5 h-5 text-accent-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              AI-Powered Collaboration
            </div>
            <div class="flex items-center text-white/90">
              <svg class="w-5 h-5 text-accent-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              Unified Workspace
            </div>
            <div class="flex items-center text-white/90">
              <svg class="w-5 h-5 text-accent-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              Enterprise Security
            </div>
            <div class="flex items-center text-white/90">
              <svg class="w-5 h-5 text-accent-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              Real-time Analytics
            </div>
          </div>

          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4">
            <a href="#trial" class="btn btn-lg bg-white text-primary-500 hover:bg-neutral-100 shadow-xl hover-lift min-h-[56px] px-8 font-semibold">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
              Start Free Trial
            </a>
            <a href="#demo" class="btn btn-lg btn-outline border-white text-white hover:bg-white hover:text-primary-500 min-h-[56px] px-8 font-semibold">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M5 12a7 7 0 1114 0v5a1 1 0 01-1 1H6a1 1 0 01-1-1v-5z"></path>
              </svg>
              Watch Demo
            </a>
          </div>

          <!-- Trust Indicators -->
          <div class="mt-8 pt-8 border-t border-white/20">
            <p class="text-white/70 text-sm mb-4">Trusted by 10,000+ teams worldwide</p>
            <div class="flex items-center space-x-6 opacity-70">
              <div class="text-white/60 text-sm font-medium">Microsoft</div>
              <div class="text-white/60 text-sm font-medium">Google</div>
              <div class="text-white/60 text-sm font-medium">Amazon</div>
              <div class="text-white/60 text-sm font-medium">Spotify</div>
            </div>
          </div>
        </div>

        <!-- Right Column - Visual -->
        <div class="relative animate-slide-up" style="animation-delay: 0.3s;">
          <!-- Dashboard Preview -->
          <div class="relative">
            <!-- Main Dashboard Card -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 shadow-2xl border border-white/20">
              <!-- Dashboard Header -->
              <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-accent-400 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-white font-semibold">AI Workspace</h3>
                    <p class="text-white/60 text-sm">Live Dashboard</p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-accent-400 rounded-full animate-pulse"></div>
                  <span class="text-white/80 text-sm">Live</span>
                </div>
              </div>

              <!-- Stats Grid -->
              <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="bg-white/5 rounded-lg p-4">
                  <div class="text-2xl font-bold text-white">1,247</div>
                  <div class="text-white/60 text-sm">Tasks Completed</div>
                  <div class="text-accent-400 text-xs">+12% this week</div>
                </div>
                <div class="bg-white/5 rounded-lg p-4">
                  <div class="text-2xl font-bold text-white">94%</div>
                  <div class="text-white/60 text-sm">Efficiency Score</div>
                  <div class="text-accent-400 text-xs">+5% improvement</div>
                </div>
              </div>

              <!-- AI Chat Preview -->
              <div class="bg-white/5 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                  <div class="w-6 h-6 bg-secondary-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                  </div>
                  <span class="text-white/80 text-sm font-medium">AI Assistant</span>
                  <div class="flex space-x-1">
                    <div class="w-1 h-1 bg-white/60 rounded-full animate-bounce"></div>
                    <div class="w-1 h-1 bg-white/60 rounded-full animate-bounce" style="animation-delay: 0.1s;"></div>
                    <div class="w-1 h-1 bg-white/60 rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
                  </div>
                </div>
                <p class="text-white/70 text-sm">I've analyzed your project timeline and suggest optimizing the development workflow...</p>
              </div>
            </div>

            <!-- Floating Elements -->
            <div class="absolute -top-4 -right-4 w-16 h-16 bg-accent-400/20 rounded-full blur-xl animate-pulse"></div>
            <div class="absolute -bottom-4 -left-4 w-12 h-12 bg-secondary-500/20 rounded-full blur-xl animate-pulse" style="animation-delay: 1s;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
