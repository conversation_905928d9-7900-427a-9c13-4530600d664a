# Professional Color Palette & Utility Reference

## 🎨 Color Palette

### Primary Colors
- **Primary Blue**: `bg-primary-500` (#0066FF) - Main brand color
- **Primary Dark**: `bg-primary-600` (#003D99) - Hover states
- **Primary Light**: `bg-primary-400` (#3385FF) - Light accents

### Secondary Colors
- **Secondary Purple**: `bg-secondary-500` (#6366F1) - Secondary actions
- **Accent Teal**: `bg-accent-500` (#06B6D4) - Highlights
- **Success Green**: `bg-success-500` (#10B981) - Success states
- **Warning Orange**: `bg-warning-500` (#F59E0B) - Warning states
- **Error Red**: `bg-error-500` (#DC2626) - Error states

### Neutral Colors
- **Neutral 50**: `bg-neutral-50` (#F8FAFC) - Lightest background
- **Neutral 100**: `bg-neutral-100` (#F1F5F9) - Light background
- **Neutral 200**: `bg-neutral-200` (#E2E8F0) - Border light
- **Neutral 300**: `bg-neutral-300` (#CBD5E1) - Border medium
- **Neutral 400**: `bg-neutral-400` (#94A3B8) - Border dark
- **Neutral 500**: `bg-neutral-500` (#64748B) - Text muted
- **Neutral 600**: `bg-neutral-600` (#475569) - Text secondary
- **Neutral 700**: `bg-neutral-700` (#334155) - Text primary
- **Neutral 800**: `bg-neutral-800` (#1E293B) - Dark background
- **Neutral 900**: `bg-neutral-900` (#0F172A) - Darkest

## 🧩 Component Classes

### Buttons
```html
<button class="btn btn-primary">Primary Button</button>
<button class="btn btn-secondary">Secondary Button</button>
<button class="btn btn-outline">Outline Button</button>
<button class="btn btn-ghost">Ghost Button</button>
<button class="btn btn-primary btn-lg">Large Button</button>
<button class="btn btn-primary btn-sm">Small Button</button>
```

### Cards
```html
<div class="card">Basic Card</div>
<div class="card card-elevated">Elevated Card</div>
<div class="card-glass">Glass Card</div>
<div class="card hover-lift">Hover Lift Card</div>
```

### Form Elements
```html
<input type="text" class="input" placeholder="Basic Input">
<input type="text" class="input input-error" placeholder="Error Input">
<select class="input">...</select>
```

### Badges
```html
<span class="badge badge-primary">Primary</span>
<span class="badge badge-success">Success</span>
<span class="badge badge-warning">Warning</span>
<span class="badge badge-error">Error</span>
```

### Alerts
```html
<div class="alert alert-info">Info message</div>
<div class="alert alert-success">Success message</div>
<div class="alert alert-warning">Warning message</div>
<div class="alert alert-error">Error message</div>
```

### Navigation
```html
<a href="#" class="nav-link">Navigation Link</a>
<a href="#" class="nav-link active">Active Link</a>
```

## 🎭 Design Effects

### Gradients
```html
<div class="gradient-primary">Primary Gradient</div>
<div class="gradient-secondary">Secondary Gradient</div>
<div class="gradient-hero">Hero Gradient</div>
<span class="text-gradient">Gradient Text</span>
```

### Glass Morphism
```html
<div class="glass">Glass Effect</div>
<div class="glass-dark">Dark Glass Effect</div>
```

### Neumorphism
```html
<div class="neuro">Soft UI Effect</div>
<div class="neuro-inset">Inset Effect</div>
```

### Shadows
```html
<div class="shadow-primary">Primary Shadow</div>
<div class="shadow-secondary">Secondary Shadow</div>
<div class="shadow-colored">Colored Shadow</div>
```

### Hover Effects
```html
<div class="hover-lift">Lift on Hover</div>
<div class="hover-scale">Scale on Hover</div>
<div class="hover-glow">Glow on Hover</div>
```

### Animations
```html
<div class="animate-fade-in">Fade In</div>
<div class="animate-slide-up">Slide Up</div>
<div class="animate-scale-in">Scale In</div>
<div class="animate-bounce-in">Bounce In</div>
```

## 🎯 Layout Utilities

### Container & Spacing
```html
<div class="container-custom">Responsive Container</div>
<section class="section-padding">Section Padding</section>
```

### Grid Systems
```html
<div class="grid grid-auto-fit gap-6">Auto Fit Grid</div>
<div class="grid grid-auto-fill gap-4">Auto Fill Grid</div>
```

### Focus States
```html
<button class="focus-ring">Focus Ring</button>
<input class="focus-ring-inset">Inset Focus Ring</input>
```

## 🌙 Dark Theme Support

Toggle dark theme by adding `data-theme="dark"` to any parent element:

```html
<div data-theme="dark">
  <!-- All colors automatically adjust for dark theme -->
</div>
```

## 📱 Responsive Design

All utilities work with responsive prefixes:
- `sm:` - Small screens (640px+)
- `md:` - Medium screens (768px+)
- `lg:` - Large screens (1024px+)
- `xl:` - Extra large screens (1280px+)

Example:
```html
<div class="bg-primary-500 md:bg-secondary-500 lg:bg-accent-500">
  Responsive Background Colors
</div>
```

## 🎨 CSS Custom Properties

Access colors directly in CSS:
```css
.custom-element {
  background: var(--color-primary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-primary);
}
```

## ♿ Accessibility

All colors meet WCAG 2.1 AA standards:
- Minimum 4.5:1 contrast ratio for normal text
- Minimum 3:1 contrast ratio for large text
- Color-blind friendly combinations
- High contrast mode support
