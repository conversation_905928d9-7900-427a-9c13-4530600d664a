import { Injectable } from '@angular/core';
import { SANITIZATION_PATTERNS, FORM_VALIDATION } from '../../security.config';

@Injectable({
  providedIn: 'root'
})
export class SecurityService {

  constructor() { }

  /**
   * Sanitize user input to prevent XSS attacks
   */
  sanitizeInput(input: string, type: 'email' | 'phone' | 'name' | 'company' | 'message' = 'message'): string {
    if (!input) return '';
    
    // Remove potentially dangerous characters
    let sanitized = input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();

    // Apply pattern-specific validation
    const pattern = SANITIZATION_PATTERNS[type];
    if (pattern && !pattern.test(sanitized)) {
      throw new Error(`Invalid ${type} format`);
    }

    return sanitized;
  }

  /**
   * Validate form data with security checks
   */
  validateFormData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for required fields
    if (!data.email || !data.name) {
      errors.push('Required fields are missing');
    }

    // Validate email format
    if (data.email && !SANITIZATION_PATTERNS.email.test(data.email)) {
      errors.push('Invalid email format');
    }

    // Validate name format
    if (data.name && !SANITIZATION_PATTERNS.name.test(data.name)) {
      errors.push('Invalid name format');
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /data:text\/html/i,
      /vbscript:/i
    ];

    for (const field in data) {
      const value = String(data[field]);
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(value)) {
          errors.push(`Suspicious content detected in ${field}`);
          break;
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate Content Security Policy header
   */
  generateCSPHeader(): string {
    const cspDirectives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://www.googletagmanager.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://api.yourdomain.com",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ];

    return cspDirectives.join('; ');
  }

  /**
   * Rate limiting check (client-side)
   */
  checkRateLimit(key: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get existing requests from localStorage
    const requestsKey = `rate_limit_${key}`;
    const existingRequests = JSON.parse(localStorage.getItem(requestsKey) || '[]');
    
    // Filter out old requests
    const recentRequests = existingRequests.filter((timestamp: number) => timestamp > windowStart);
    
    // Check if limit exceeded
    if (recentRequests.length >= maxRequests) {
      return false;
    }
    
    // Add current request
    recentRequests.push(now);
    localStorage.setItem(requestsKey, JSON.stringify(recentRequests));
    
    return true;
  }

  /**
   * Secure cookie settings
   */
  setSecureCookie(name: string, value: string, days: number = 1): void {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    
    const cookieString = `${name}=${value}; expires=${expires.toUTCString()}; path=/; secure; samesite=strict`;
    document.cookie = cookieString;
  }

  /**
   * Log security events
   */
  logSecurityEvent(event: string, details: any): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // In production, send to security monitoring service
    console.warn('Security Event:', logEntry);
    
    // Store locally for debugging (remove in production)
    const logs = JSON.parse(localStorage.getItem('security_logs') || '[]');
    logs.push(logEntry);
    
    // Keep only last 100 logs
    if (logs.length > 100) {
      logs.splice(0, logs.length - 100);
    }
    
    localStorage.setItem('security_logs', JSON.stringify(logs));
  }

  /**
   * Check if HTTPS is enforced
   */
  enforceHTTPS(): void {
    if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
      location.replace(`https:${location.href.substring(location.protocol.length)}`);
    }
  }

  /**
   * Validate file upload security
   */
  validateFileUpload(file: File): { isValid: boolean; error?: string } {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      return { isValid: false, error: 'File type not allowed' };
    }

    if (file.size > maxSize) {
      return { isValid: false, error: 'File size too large' };
    }

    // Check for suspicious file names
    const suspiciousPatterns = [
      /\.php$/i,
      /\.js$/i,
      /\.html$/i,
      /\.exe$/i,
      /\.bat$/i,
      /\.cmd$/i
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(file.name)) {
        return { isValid: false, error: 'Suspicious file name' };
      }
    }

    return { isValid: true };
  }

  /**
   * Generate secure random token
   */
  generateSecureToken(length: number = 32): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Check for common security headers
   */
  checkSecurityHeaders(): Promise<{ [key: string]: boolean }> {
    return fetch(window.location.href, { method: 'HEAD' })
      .then(response => {
        const headers = response.headers;
        return {
          'X-Content-Type-Options': headers.has('x-content-type-options'),
          'X-Frame-Options': headers.has('x-frame-options'),
          'X-XSS-Protection': headers.has('x-xss-protection'),
          'Strict-Transport-Security': headers.has('strict-transport-security'),
          'Content-Security-Policy': headers.has('content-security-policy')
        };
      })
      .catch(() => ({}));
  }
}
