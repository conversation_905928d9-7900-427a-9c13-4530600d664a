/* Resources Section Styles */

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Code blocks */
.code-block {
  background-color: #1a1a1a;
  border-radius: 0.5rem;
  padding: 0.75rem;
  font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  overflow-x: auto;
}

.code-block code {
  color: #10b981;
}

/* Card hover effects */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* API method badges */
.method-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  text-transform: uppercase;
}

/* Tutorial cards */
.tutorial-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e5e5;
}

.tutorial-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Resource icons */
.resource-icon {
  transition: all 0.3s ease;
}

.resource-icon:hover {
  transform: scale(1.1);
}

/* Gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Animation for cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .code-block {
    font-size: 0.625rem;
    padding: 0.5rem;
  }

  .tutorial-card {
    margin-bottom: 1rem;
  }
}

/* Border colors for different sections */
.border-primary-accent {
  border-left-color: #3b82f6;
}

.border-secondary-accent {
  border-left-color: #8b5cf6;
}

.border-accent-accent {
  border-left-color: #10b981;
}

/* Button hover effects */
.btn-hover-scale {
  transition: transform 0.2s ease;
}

.btn-hover-scale:hover {
  transform: scale(1.05);
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Custom scrollbar for code blocks */
.code-block::-webkit-scrollbar {
  height: 4px;
}

.code-block::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.code-block::-webkit-scrollbar-thumb {
  background: #10b981;
  border-radius: 2px;
}

.code-block::-webkit-scrollbar-thumb:hover {
  background: #059669;
}
