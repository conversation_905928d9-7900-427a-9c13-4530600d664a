/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,ts}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'sans': ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
        'inter': ['Inter', 'sans-serif'],
      },
      colors: {
        // AI Hub Primary Colors - Deep Indigo
        primary: {
          50: '#F0F1FF',
          100: '#E1E4FF',
          200: '#C7CDFF',
          300: '#A5AFFF',
          400: '#8B94FF',
          500: '#2A2E5B', // Deep Indigo - Main brand
          600: '#242847',
          700: '#1E2139',
          800: '#181B2B',
          900: '#12141D',
        },
        // Electric Blue - AI-inspired accent
        secondary: {
          50: '#F0F9FF',
          100: '#E0F2FE',
          200: '#BAE6FD',
          300: '#7DD3FC',
          400: '#38BDF8',
          500: '#3C9EE7', // Electric Blue - Main accent
          600: '#0284C7',
          700: '#0369A1',
          800: '#075985',
          900: '#0C4A6E',
        },
        // Emerald Green - Positive actions
        accent: {
          50: '#ECFDF5',
          100: '#D1FAE5',
          200: '#A7F3D0',
          300: '#6EE7B7',
          400: '#34D399', // Emerald Green - Success/CTA
          500: '#10B981',
          600: '#059669',
          700: '#047857',
          800: '#065F46',
          900: '#064E3B',
        },
        // Amber/Gold - Highlights and alerts
        warning: {
          50: '#FFFBEB',
          100: '#FEF3C7',
          200: '#FDE68A',
          300: '#FCD34D',
          400: '#FBBF24', // Amber/Gold - Highlights
          500: '#F59E0B',
          600: '#D97706',
          700: '#B45309',
          800: '#92400E',
          900: '#78350F',
        },
        // Success (using accent green)
        success: {
          50: '#ECFDF5',
          100: '#D1FAE5',
          200: '#A7F3D0',
          300: '#6EE7B7',
          400: '#34D399', // Emerald Green
          500: '#10B981',
          600: '#059669',
          700: '#047857',
          800: '#065F46',
          900: '#064E3B',
        },
        // Error Red
        error: {
          50: '#FEF2F2',
          100: '#FEE2E2',
          200: '#FECACA',
          300: '#FCA5A5',
          400: '#F87171',
          500: '#DC2626', // Main error
          600: '#B91C1C',
          700: '#991B1B',
          800: '#7F1D1D',
          900: '#7C2D12',
        },
        // AI Hub Neutrals - Professional grays
        neutral: {
          50: '#F9FAFB',  // Cloud White - Clean background
          100: '#F3F4F6',  // Card backgrounds
          200: '#E5E7EB',  // Light borders
          300: '#D1D5DB',  // Dividers, borders
          400: '#9CA3AF',  // Disabled states
          500: '#6B7280',  // Subtext, muted UI elements
          600: '#4B5563',  // Secondary text
          700: '#374151',  // Body text
          800: '#1F2937',  // Dark text
          900: '#111827',  // Charcoal Gray - Rich text
        },
        // Dark Theme Colors
        dark: {
          primary: '#0A0A0B',
          secondary: '#1A1A1B',
          tertiary: '#2A2A2B',
          surface: '#1F1F20',
          border: '#2F2F30',
        }
      },
    },
  },
  plugins: [],
}
