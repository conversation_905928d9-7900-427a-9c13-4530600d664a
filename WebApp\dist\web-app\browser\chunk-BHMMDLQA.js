import{b as fe,i as Ge,j as je}from"./chunk-XP6X4NQJ.js";import{a as Re}from"./chunk-CUNQO24F.js";import{$ as k,$a as r,Ab as H,Fa as c,J as Y,Ja as R,K as A,Ka as m,M as ue,O as I,Pa as Z,Qa as me,Ra as v,Sa as V,Ta as Ie,Va as Oe,W as ce,X as p,Y as g,Ya as Pe,Z as h,Za as J,_ as f,_a as _,a as y,ab as o,b,bb as u,cb as Ne,db as C,ea as T,g as De,ia as G,ib as ke,j as Ee,ja as j,jb as a,nb as w,o as Ae,ob as S,pb as M,qb as F,u as Fe,vb as Te,yb as he,zb as D}from"./chunk-RZLO7X7E.js";var Je=(()=>{class n{_renderer;_elementRef;onChange=e=>{};onTouched=()=>{};constructor(e,i){this._renderer=e,this._elementRef=i}setProperty(e,i){this._renderer.setProperty(this._elementRef.nativeElement,e,i)}registerOnTouched(e){this.onTouched=e}registerOnChange(e){this.onChange=e}setDisabledState(e){this.setProperty("disabled",e)}static \u0275fac=function(i){return new(i||n)(m(R),m(G))};static \u0275dir=v({type:n})}return n})(),_e=(()=>{class n extends Je{static \u0275fac=(()=>{let e;return function(s){return(e||(e=k(n)))(s||n)}})();static \u0275dir=v({type:n,features:[V]})}return n})(),re=new I("");var vt={provide:re,useExisting:A(()=>oe),multi:!0};function _t(){let n=fe()?fe().getUserAgent():"";return/android (\d+)/.test(n.toLowerCase())}var yt=new I(""),oe=(()=>{class n extends Je{_compositionMode;_composing=!1;constructor(e,i,s){super(e,i),this._compositionMode=s,this._compositionMode==null&&(this._compositionMode=!_t())}writeValue(e){let i=e??"";this.setProperty("value",i)}_handleInput(e){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(e)}_compositionStart(){this._composing=!0}_compositionEnd(e){this._composing=!1,this._compositionMode&&this.onChange(e)}static \u0275fac=function(i){return new(i||n)(m(R),m(G),m(yt,8))};static \u0275dir=v({type:n,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(i,s){i&1&&C("input",function(l){return s._handleInput(l.target.value)})("blur",function(){return s.onTouched()})("compositionstart",function(){return s._compositionStart()})("compositionend",function(l){return s._compositionEnd(l.target.value)})},standalone:!1,features:[F([vt]),V]})}return n})();function Ct(n){return n==null||bt(n)===0}function bt(n){return n==null?null:Array.isArray(n)||typeof n=="string"?n.length:n instanceof Set?n.size:null}var ye=new I(""),Xe=new I("");function Vt(n){return Ct(n.value)?{required:!0}:null}function He(n){return null}function Ke(n){return n!=null}function Qe(n){return Oe(n)?Ee(n):n}function et(n){let t={};return n.forEach(e=>{t=e!=null?y(y({},t),e):t}),Object.keys(t).length===0?null:t}function tt(n,t){return t.map(e=>e(n))}function wt(n){return!n.validate}function nt(n){return n.map(t=>wt(t)?t:e=>t.validate(e))}function St(n){if(!n)return null;let t=n.filter(Ke);return t.length==0?null:function(e){return et(tt(e,t))}}function Ce(n){return n!=null?St(nt(n)):null}function Mt(n){if(!n)return null;let t=n.filter(Ke);return t.length==0?null:function(e){let i=tt(e,t).map(Qe);return Fe(i).pipe(Ae(et))}}function be(n){return n!=null?Mt(nt(n)):null}function Be(n,t){return n===null?[t]:Array.isArray(n)?[...n,t]:[n,t]}function xt(n){return n._rawValidators}function Dt(n){return n._rawAsyncValidators}function pe(n){return n?Array.isArray(n)?n:[n]:[]}function K(n,t){return Array.isArray(n)?n.includes(t):n===t}function Le(n,t){let e=pe(t);return pe(n).forEach(s=>{K(e,s)||e.push(s)}),e}function Ue(n,t){return pe(t).filter(e=>!K(n,e))}var Q=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=Ce(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=be(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,e){return this.control?this.control.hasError(t,e):!1}getError(t,e){return this.control?this.control.getError(t,e):null}},N=class extends Q{name;get formDirective(){return null}get path(){return null}},z=class extends Q{_parent=null;name=null;valueAccessor=null},ee=class{_cd;constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},Et={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},wn=b(y({},Et),{"[class.ng-submitted]":"isSubmitted"}),it=(()=>{class n extends ee{constructor(e){super(e)}static \u0275fac=function(i){return new(i||n)(m(z,2))};static \u0275dir=v({type:n,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(i,s){i&2&&_("ng-untouched",s.isUntouched)("ng-touched",s.isTouched)("ng-pristine",s.isPristine)("ng-dirty",s.isDirty)("ng-valid",s.isValid)("ng-invalid",s.isInvalid)("ng-pending",s.isPending)},standalone:!1,features:[V]})}return n})(),rt=(()=>{class n extends ee{constructor(e){super(e)}static \u0275fac=function(i){return new(i||n)(m(N,10))};static \u0275dir=v({type:n,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(i,s){i&2&&_("ng-untouched",s.isUntouched)("ng-touched",s.isTouched)("ng-pristine",s.isPristine)("ng-dirty",s.isDirty)("ng-valid",s.isValid)("ng-invalid",s.isInvalid)("ng-pending",s.isPending)("ng-submitted",s.isSubmitted)},standalone:!1,features:[V]})}return n})();var B="VALID",X="INVALID",O="PENDING",L="DISABLED",E=class{},te=class extends E{value;source;constructor(t,e){super(),this.value=t,this.source=e}},W=class extends E{pristine;source;constructor(t,e){super(),this.pristine=t,this.source=e}},q=class extends E{touched;source;constructor(t,e){super(),this.touched=t,this.source=e}},P=class extends E{status;source;constructor(t,e){super(),this.status=t,this.source=e}},ge=class extends E{source;constructor(t){super(),this.source=t}},ve=class extends E{source;constructor(t){super(),this.source=t}};function ot(n){return(se(n)?n.validators:n)||null}function At(n){return Array.isArray(n)?Ce(n):n||null}function st(n,t){return(se(t)?t.asyncValidators:n)||null}function Ft(n){return Array.isArray(n)?be(n):n||null}function se(n){return n!=null&&!Array.isArray(n)&&typeof n=="object"}function It(n,t,e){let i=n.controls;if(!(t?Object.keys(i):i).length)throw new Y(1e3,"");if(!i[e])throw new Y(1001,"")}function Ot(n,t,e){n._forEachChild((i,s)=>{if(e[s]===void 0)throw new Y(1002,"")})}var ne=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(t,e){this._assignValidators(t),this._assignAsyncValidators(e)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return D(this.statusReactive)}set status(t){D(()=>this.statusReactive.set(t))}_status=H(()=>this.statusReactive());statusReactive=j(void 0);get valid(){return this.status===B}get invalid(){return this.status===X}get pending(){return this.status==O}get disabled(){return this.status===L}get enabled(){return this.status!==L}errors;get pristine(){return D(this.pristineReactive)}set pristine(t){D(()=>this.pristineReactive.set(t))}_pristine=H(()=>this.pristineReactive());pristineReactive=j(!0);get dirty(){return!this.pristine}get touched(){return D(this.touchedReactive)}set touched(t){D(()=>this.touchedReactive.set(t))}_touched=H(()=>this.touchedReactive());touchedReactive=j(!1);get untouched(){return!this.touched}_events=new De;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Le(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Le(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(Ue(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(Ue(t,this._rawAsyncValidators))}hasValidator(t){return K(this._rawValidators,t)}hasAsyncValidator(t){return K(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let e=this.touched===!1;this.touched=!0;let i=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(b(y({},t),{sourceControl:i})),e&&t.emitEvent!==!1&&this._events.next(new q(!0,i))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(e=>e.markAllAsTouched(t))}markAsUntouched(t={}){let e=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let i=t.sourceControl??this;this._forEachChild(s=>{s.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:i})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,i),e&&t.emitEvent!==!1&&this._events.next(new q(!1,i))}markAsDirty(t={}){let e=this.pristine===!0;this.pristine=!1;let i=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(b(y({},t),{sourceControl:i})),e&&t.emitEvent!==!1&&this._events.next(new W(!1,i))}markAsPristine(t={}){let e=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let i=t.sourceControl??this;this._forEachChild(s=>{s.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,i),e&&t.emitEvent!==!1&&this._events.next(new W(!0,i))}markAsPending(t={}){this.status=O;let e=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new P(this.status,e)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(b(y({},t),{sourceControl:e}))}disable(t={}){let e=this._parentMarkedDirty(t.onlySelf);this.status=L,this.errors=null,this._forEachChild(s=>{s.disable(b(y({},t),{onlySelf:!0}))}),this._updateValue();let i=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new te(this.value,i)),this._events.next(new P(this.status,i)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(b(y({},t),{skipPristineCheck:e}),this),this._onDisabledChange.forEach(s=>s(!0))}enable(t={}){let e=this._parentMarkedDirty(t.onlySelf);this.status=B,this._forEachChild(i=>{i.enable(b(y({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(b(y({},t),{skipPristineCheck:e}),this),this._onDisabledChange.forEach(i=>i(!1))}_updateAncestors(t,e){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},e),this._parent._updateTouched({},e))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let i=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===B||this.status===O)&&this._runAsyncValidator(i,t.emitEvent)}let e=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new te(this.value,e)),this._events.next(new P(this.status,e)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(b(y({},t),{sourceControl:e}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(e=>e._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?L:B}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,e){if(this.asyncValidator){this.status=O,this._hasOwnPendingAsyncValidator={emitEvent:e!==!1};let i=Qe(this.asyncValidator(this));this._asyncValidationSubscription=i.subscribe(s=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(s,{emitEvent:e,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,e={}){this.errors=t,this._updateControlsErrors(e.emitEvent!==!1,this,e.shouldHaveEmitted)}get(t){let e=t;return e==null||(Array.isArray(e)||(e=e.split(".")),e.length===0)?null:e.reduce((i,s)=>i&&i._find(s),this)}getError(t,e){let i=e?this.get(e):this;return i&&i.errors?i.errors[t]:null}hasError(t,e){return!!this.getError(t,e)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,e,i){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||i)&&this._events.next(new P(this.status,e)),this._parent&&this._parent._updateControlsErrors(t,e,i)}_initObservables(){this.valueChanges=new T,this.statusChanges=new T}_calculateStatus(){return this._allControlsDisabled()?L:this.errors?X:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(O)?O:this._anyControlsHaveStatus(X)?X:B}_anyControlsHaveStatus(t){return this._anyControls(e=>e.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,e){let i=!this._anyControlsDirty(),s=this.pristine!==i;this.pristine=i,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,e),s&&this._events.next(new W(this.pristine,e))}_updateTouched(t={},e){this.touched=this._anyControlsTouched(),this._events.next(new q(this.touched,e)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,e)}_onDisabledChange=[];_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){se(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let e=this._parent&&this._parent.dirty;return!t&&!!e&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=At(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=Ft(this._rawAsyncValidators)}},ie=class extends ne{constructor(t,e,i){super(ot(e),st(i,e)),this.controls=t,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(t,e){return this.controls[t]?this.controls[t]:(this.controls[t]=e,e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange),e)}addControl(t,e,i={}){this.registerControl(t,e),this.updateValueAndValidity({emitEvent:i.emitEvent}),this._onCollectionChange()}removeControl(t,e={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}setControl(t,e,i={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],e&&this.registerControl(t,e),this.updateValueAndValidity({emitEvent:i.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,e={}){Ot(this,!0,t),Object.keys(t).forEach(i=>{It(this,!0,i),this.controls[i].setValue(t[i],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(t,e={}){t!=null&&(Object.keys(t).forEach(i=>{let s=this.controls[i];s&&s.patchValue(t[i],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(t={},e={}){this._forEachChild((i,s)=>{i.reset(t?t[s]:null,{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e,this),this._updateTouched(e,this),this.updateValueAndValidity(e)}getRawValue(){return this._reduceChildren({},(t,e,i)=>(t[i]=e.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(e,i)=>i._syncPendingControls()?!0:e);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(e=>{let i=this.controls[e];i&&t(i,e)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[e,i]of Object.entries(this.controls))if(this.contains(e)&&t(i))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(e,i,s)=>((i.enabled||this.disabled)&&(e[s]=i.value),e))}_reduceChildren(t,e){let i=t;return this._forEachChild((s,d)=>{i=e(i,s,d)}),i}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var Ve=new I("",{providedIn:"root",factory:()=>we}),we="always";function Pt(n,t){return[...t.path,n]}function at(n,t,e=we){lt(n,t),t.valueAccessor.writeValue(n.value),(n.disabled||e==="always")&&t.valueAccessor.setDisabledState?.(n.disabled),kt(n,t),Gt(n,t),Tt(n,t),Nt(n,t)}function We(n,t){n.forEach(e=>{e.registerOnValidatorChange&&e.registerOnValidatorChange(t)})}function Nt(n,t){if(t.valueAccessor.setDisabledState){let e=i=>{t.valueAccessor.setDisabledState(i)};n.registerOnDisabledChange(e),t._registerOnDestroy(()=>{n._unregisterOnDisabledChange(e)})}}function lt(n,t){let e=xt(n);t.validator!==null?n.setValidators(Be(e,t.validator)):typeof e=="function"&&n.setValidators([e]);let i=Dt(n);t.asyncValidator!==null?n.setAsyncValidators(Be(i,t.asyncValidator)):typeof i=="function"&&n.setAsyncValidators([i]);let s=()=>n.updateValueAndValidity();We(t._rawValidators,s),We(t._rawAsyncValidators,s)}function kt(n,t){t.valueAccessor.registerOnChange(e=>{n._pendingValue=e,n._pendingChange=!0,n._pendingDirty=!0,n.updateOn==="change"&&dt(n,t)})}function Tt(n,t){t.valueAccessor.registerOnTouched(()=>{n._pendingTouched=!0,n.updateOn==="blur"&&n._pendingChange&&dt(n,t),n.updateOn!=="submit"&&n.markAsTouched()})}function dt(n,t){n._pendingDirty&&n.markAsDirty(),n.setValue(n._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(n._pendingValue),n._pendingChange=!1}function Gt(n,t){let e=(i,s)=>{t.valueAccessor.writeValue(i),s&&t.viewToModelUpdate(i)};n.registerOnChange(e),t._registerOnDestroy(()=>{n._unregisterOnChange(e)})}function jt(n,t){n==null,lt(n,t)}function Rt(n,t){if(!n.hasOwnProperty("model"))return!1;let e=n.model;return e.isFirstChange()?!0:!Object.is(t,e.currentValue)}function Ht(n){return Object.getPrototypeOf(n.constructor)===_e}function Bt(n,t){n._syncPendingControls(),t.forEach(e=>{let i=e.control;i.updateOn==="submit"&&i._pendingChange&&(e.viewToModelUpdate(i._pendingValue),i._pendingChange=!1)})}function Lt(n,t){if(!t)return null;Array.isArray(t);let e,i,s;return t.forEach(d=>{d.constructor===oe?e=d:Ht(d)?i=d:s=d}),s||i||e||null}var Ut={provide:N,useExisting:A(()=>Se)},U=Promise.resolve(),Se=(()=>{class n extends N{callSetDisabledState;get submitted(){return D(this.submittedReactive)}_submitted=H(()=>this.submittedReactive());submittedReactive=j(!1);_directives=new Set;form;ngSubmit=new T;options;constructor(e,i,s){super(),this.callSetDisabledState=s,this.form=new ie({},Ce(e),be(i))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(e){U.then(()=>{let i=this._findContainer(e.path);e.control=i.registerControl(e.name,e.control),at(e.control,e,this.callSetDisabledState),e.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(e)})}getControl(e){return this.form.get(e.path)}removeControl(e){U.then(()=>{let i=this._findContainer(e.path);i&&i.removeControl(e.name),this._directives.delete(e)})}addFormGroup(e){U.then(()=>{let i=this._findContainer(e.path),s=new ie({});jt(s,e),i.registerControl(e.name,s),s.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(e){U.then(()=>{let i=this._findContainer(e.path);i&&i.removeControl(e.name)})}getFormGroup(e){return this.form.get(e.path)}updateModel(e,i){U.then(()=>{this.form.get(e.path).setValue(i)})}setValue(e){this.control.setValue(e)}onSubmit(e){return this.submittedReactive.set(!0),Bt(this.form,this._directives),this.ngSubmit.emit(e),this.form._events.next(new ge(this.control)),e?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this.submittedReactive.set(!1),this.form._events.next(new ve(this.form))}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(e){return e.pop(),e.length?this.form.get(e):this.form}static \u0275fac=function(i){return new(i||n)(m(ye,10),m(Xe,10),m(Ve,8))};static \u0275dir=v({type:n,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(i,s){i&1&&C("submit",function(l){return s.onSubmit(l)})("reset",function(){return s.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[F([Ut]),V]})}return n})();function qe(n,t){let e=n.indexOf(t);e>-1&&n.splice(e,1)}function ze(n){return typeof n=="object"&&n!==null&&Object.keys(n).length===2&&"value"in n&&"disabled"in n}var Wt=class extends ne{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(t=null,e,i){super(ot(e),st(i,e)),this._applyFormState(t),this._setUpdateStrategy(e),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),se(e)&&(e.nonNullable||e.initialValueIsDefault)&&(ze(t)?this.defaultValue=t.value:this.defaultValue=t)}setValue(t,e={}){this.value=this._pendingValue=t,this._onChange.length&&e.emitModelToViewChange!==!1&&this._onChange.forEach(i=>i(this.value,e.emitViewToModelChange!==!1)),this.updateValueAndValidity(e)}patchValue(t,e={}){this.setValue(t,e)}reset(t=this.defaultValue,e={}){this._applyFormState(t),this.markAsPristine(e),this.markAsUntouched(e),this.setValue(this.value,e),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){qe(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){qe(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(t){ze(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};var qt={provide:z,useExisting:A(()=>Me)},$e=Promise.resolve(),Me=(()=>{class n extends z{_changeDetectorRef;callSetDisabledState;control=new Wt;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new T;constructor(e,i,s,d,l,x){super(),this._changeDetectorRef=l,this.callSetDisabledState=x,this._parent=e,this._setValidators(i),this._setAsyncValidators(s),this.valueAccessor=Lt(this,d)}ngOnChanges(e){if(this._checkForErrors(),!this._registered||"name"in e){if(this._registered&&(this._checkName(),this.formDirective)){let i=e.name.previousValue;this.formDirective.removeControl({name:i,path:this._getPath(i)})}this._setUpControl()}"isDisabled"in e&&this._updateDisabled(e),Rt(e,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){at(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(e){$e.then(()=>{this.control.setValue(e,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(e){let i=e.isDisabled.currentValue,s=i!==0&&he(i);$e.then(()=>{s&&!this.control.disabled?this.control.disable():!s&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(e){return this._parent?Pt(e,this._parent):[e]}static \u0275fac=function(i){return new(i||n)(m(N,9),m(ye,10),m(Xe,10),m(re,10),m(Te,8),m(Ve,8))};static \u0275dir=v({type:n,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[F([qt]),V,ce]})}return n})();var ut=(()=>{class n{static \u0275fac=function(i){return new(i||n)};static \u0275dir=v({type:n,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return n})();var zt={provide:re,useExisting:A(()=>ae),multi:!0};function ct(n,t){return n==null?`${t}`:(t&&typeof t=="object"&&(t="Object"),`${n}: ${t}`.slice(0,50))}function $t(n){return n.split(":")[0]}var ae=(()=>{class n extends _e{value;_optionMap=new Map;_idCounter=0;set compareWith(e){this._compareWith=e}_compareWith=Object.is;writeValue(e){this.value=e;let i=this._getOptionId(e),s=ct(i,e);this.setProperty("value",s)}registerOnChange(e){this.onChange=i=>{this.value=this._getOptionValue(i),e(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(e){for(let i of this._optionMap.keys())if(this._compareWith(this._optionMap.get(i),e))return i;return null}_getOptionValue(e){let i=$t(e);return this._optionMap.has(i)?this._optionMap.get(i):e}static \u0275fac=(()=>{let e;return function(s){return(e||(e=k(n)))(s||n)}})();static \u0275dir=v({type:n,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(i,s){i&1&&C("change",function(l){return s.onChange(l.target.value)})("blur",function(){return s.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[F([zt]),V]})}return n})(),mt=(()=>{class n{_element;_renderer;_select;id;constructor(e,i,s){this._element=e,this._renderer=i,this._select=s,this._select&&(this.id=this._select._registerOption())}set ngValue(e){this._select!=null&&(this._select._optionMap.set(this.id,e),this._setElementValue(ct(this.id,e)),this._select.writeValue(this._select.value))}set value(e){this._setElementValue(e),this._select&&this._select.writeValue(this._select.value)}_setElementValue(e){this._renderer.setProperty(this._element.nativeElement,"value",e)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(i){return new(i||n)(m(G),m(R),m(ae,9))};static \u0275dir=v({type:n,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return n})(),Yt={provide:re,useExisting:A(()=>ht),multi:!0};function Ye(n,t){return n==null?`${t}`:(typeof t=="string"&&(t=`'${t}'`),t&&typeof t=="object"&&(t="Object"),`${n}: ${t}`.slice(0,50))}function Zt(n){return n.split(":")[0]}var ht=(()=>{class n extends _e{value;_optionMap=new Map;_idCounter=0;set compareWith(e){this._compareWith=e}_compareWith=Object.is;writeValue(e){this.value=e;let i;if(Array.isArray(e)){let s=e.map(d=>this._getOptionId(d));i=(d,l)=>{d._setSelected(s.indexOf(l.toString())>-1)}}else i=(s,d)=>{s._setSelected(!1)};this._optionMap.forEach(i)}registerOnChange(e){this.onChange=i=>{let s=[],d=i.selectedOptions;if(d!==void 0){let l=d;for(let x=0;x<l.length;x++){let $=l[x],de=this._getOptionValue($.value);s.push(de)}}else{let l=i.options;for(let x=0;x<l.length;x++){let $=l[x];if($.selected){let de=this._getOptionValue($.value);s.push(de)}}}this.value=s,e(s)}}_registerOption(e){let i=(this._idCounter++).toString();return this._optionMap.set(i,e),i}_getOptionId(e){for(let i of this._optionMap.keys())if(this._compareWith(this._optionMap.get(i)._value,e))return i;return null}_getOptionValue(e){let i=Zt(e);return this._optionMap.has(i)?this._optionMap.get(i)._value:e}static \u0275fac=(()=>{let e;return function(s){return(e||(e=k(n)))(s||n)}})();static \u0275dir=v({type:n,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(i,s){i&1&&C("change",function(l){return s.onChange(l.target)})("blur",function(){return s.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[F([Yt]),V]})}return n})(),ft=(()=>{class n{_element;_renderer;_select;id;_value;constructor(e,i,s){this._element=e,this._renderer=i,this._select=s,this._select&&(this.id=this._select._registerOption(this))}set ngValue(e){this._select!=null&&(this._value=e,this._setElementValue(Ye(this.id,e)),this._select.writeValue(this._select.value))}set value(e){this._select?(this._value=e,this._setElementValue(Ye(this.id,e)),this._select.writeValue(this._select.value)):this._setElementValue(e)}_setElementValue(e){this._renderer.setProperty(this._element.nativeElement,"value",e)}_setSelected(e){this._renderer.setProperty(this._element.nativeElement,"selected",e)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(i){return new(i||n)(m(G),m(R),m(ht,9))};static \u0275dir=v({type:n,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return n})();var Jt=(()=>{class n{_validator=He;_onChange;_enabled;ngOnChanges(e){if(this.inputName in e){let i=this.normalizeInput(e[this.inputName].currentValue);this._enabled=this.enabled(i),this._validator=this._enabled?this.createValidator(i):He,this._onChange&&this._onChange()}}validate(e){return this._validator(e)}registerOnValidatorChange(e){this._onChange=e}enabled(e){return e!=null}static \u0275fac=function(i){return new(i||n)};static \u0275dir=v({type:n,features:[ce]})}return n})();var Xt={provide:ye,useExisting:A(()=>xe),multi:!0};var xe=(()=>{class n extends Jt{required;inputName="required";normalizeInput=he;createValidator=e=>Vt;enabled(e){return e}static \u0275fac=(()=>{let e;return function(s){return(e||(e=k(n)))(s||n)}})();static \u0275dir=v({type:n,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(i,s){i&2&&Pe("required",s._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[F([Xt]),V]})}return n})();var Kt=(()=>{class n{static \u0275fac=function(i){return new(i||n)};static \u0275mod=me({type:n});static \u0275inj=ue({})}return n})();var pt=(()=>{class n{static withConfig(e){return{ngModule:n,providers:[{provide:Ve,useValue:e.callSetDisabledState??we}]}}static \u0275fac=function(i){return new(i||n)};static \u0275mod=me({type:n});static \u0275inj=ue({imports:[Kt]})}return n})();function tn(n,t){n&1&&(r(0,"span",107),h(),r(1,"svg",5),u(2,"path",108),o(),a(3," Contact Sales Team "),o())}function nn(n,t){n&1&&(r(0,"span",107),h(),r(1,"svg",109),u(2,"circle",110)(3,"path",111),o(),a(4," Sending... "),o())}var le=class n{formData={firstName:"",lastName:"",email:"",phone:"",company:"",teamSize:"",jobTitle:"",interest:"",message:""};isSubmitting=!1;submitSuccess=!1;submitError="";openFaqIndex=null;contactMethods=[{icon:"phone",title:"Sales Hotline",description:"Speak directly with our sales experts",contact:"+1 (800) AI-HUBS",availability:"Mon-Fri, 9 AM - 6 PM EST",action:"tel:******-AI-HUBS"},{icon:"email",title:"Sales Email",description:"For detailed inquiries and proposals",contact:"<EMAIL>",availability:"Response within 4 hours",action:"mailto:<EMAIL>"},{icon:"chat",title:"Live Chat",description:"Instant support from our team",contact:"Start Chat Now",availability:"Available 24/7",action:"javascript:void(0)"}];benefits=[{title:"300% Productivity Boost",description:"Average improvement reported by our enterprise clients"},{title:"Enterprise-Grade Security",description:"SOC 2 Type II certified with end-to-end encryption"},{title:"24/7 Dedicated Support",description:"Priority support with dedicated customer success manager"},{title:"Seamless Integration",description:"Connect with 500+ tools including Slack, Microsoft Teams, and more"},{title:"Custom Implementation",description:"Tailored setup and training for your specific business needs"}];salesSteps=[{step:1,title:"Initial Contact",description:"Fill out the form or call us. We'll schedule a discovery call within 24 hours.",color:"primary"},{step:2,title:"Personalized Demo",description:"See AI Hub in action with a demo tailored to your specific use cases and needs.",color:"secondary"},{step:3,title:"Custom Proposal",description:"Receive a detailed proposal with pricing, implementation plan, and timeline.",color:"accent"},{step:4,title:"Onboarding",description:"Get started with dedicated support, training, and seamless data migration.",color:"warning"}];faqs=[{question:"How long does implementation take?",answer:"Most implementations take 2-4 weeks depending on your team size and integration requirements. Our dedicated implementation team will work with you to ensure a smooth transition with minimal disruption to your workflow."},{question:"What's included in enterprise support?",answer:"Enterprise support includes 24/7 priority support, dedicated customer success manager, custom training sessions, API support, and guaranteed 99.9% uptime SLA with priority incident response."},{question:"Can we integrate with our existing tools?",answer:"Yes! AI Hub integrates with 500+ popular tools including Slack, Microsoft Teams, Jira, Salesforce, Google Workspace, and more. We also provide REST APIs and webhooks for custom integrations."},{question:"What security measures are in place?",answer:"AI Hub is SOC 2 Type II certified with enterprise-grade security including end-to-end encryption, SSO integration, role-based access controls, audit logs, and compliance with GDPR, HIPAA, and other regulations."},{question:"Do you offer custom pricing for large teams?",answer:"Absolutely! We offer volume discounts and custom pricing for teams of 100+ users. Contact our sales team to discuss your specific needs and get a personalized quote."}];onSubmit(){this.isSubmitting||(this.isSubmitting=!0,this.submitError="",setTimeout(()=>{try{console.log("Form submitted:",this.formData),this.submitSuccess=!0,this.resetForm(),setTimeout(()=>{this.submitSuccess=!1},5e3)}catch{this.submitError="There was an error submitting your request. Please try again."}finally{this.isSubmitting=!1}},2e3))}toggleFaq(t){this.openFaqIndex=this.openFaqIndex===t?null:t}resetForm(){this.formData={firstName:"",lastName:"",email:"",phone:"",company:"",teamSize:"",jobTitle:"",interest:"",message:""}}getStepColorClass(t){return{primary:"gradient-primary",secondary:"bg-secondary-500",accent:"bg-accent-400",warning:"bg-warning-400"}[t]||"gradient-primary"}static \u0275fac=function(e){return new(e||n)};static \u0275cmp=Z({type:n,selectors:[["app-contact-sales"]],decls:318,vars:32,consts:[["contactForm","ngForm"],["id","contact-sales",1,"py-20","bg-gradient-to-br","from-neutral-50","to-white"],[1,"container-custom"],[1,"text-center","mb-16"],[1,"inline-flex","items-center","px-4","py-2","bg-primary-100","rounded-full","text-primary-700","text-sm","font-medium","mb-6"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","mr-2"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21L6.16 11.37a11.045 11.045 0 005.516 5.516l1.983-4.064a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"],[1,"text-4xl","md:text-5xl","font-bold","text-neutral-900","mb-6"],[1,"text-primary-500"],[1,"text-xl","text-neutral-600","max-w-3xl","mx-auto","leading-relaxed"],[1,"grid","lg:grid-cols-2","gap-12","mb-16"],[1,"bg-white","rounded-2xl","p-8","shadow-lg","border","border-neutral-100"],[1,"mb-8"],[1,"text-2xl","font-bold","text-neutral-900","mb-4"],[1,"text-neutral-600"],[1,"space-y-6",3,"ngSubmit"],[1,"grid","md:grid-cols-2","gap-4"],["for","firstName",1,"block","text-sm","font-medium","text-neutral-700","mb-2"],["type","text","id","firstName","name","firstName","required","","placeholder","John",1,"w-full","px-4","py-3","border","border-neutral-300","rounded-lg","focus:ring-2","focus:ring-primary-500","focus:border-primary-500","transition-colors",3,"ngModelChange","ngModel"],["for","lastName",1,"block","text-sm","font-medium","text-neutral-700","mb-2"],["type","text","id","lastName","name","lastName","required","","placeholder","Doe",1,"w-full","px-4","py-3","border","border-neutral-300","rounded-lg","focus:ring-2","focus:ring-primary-500","focus:border-primary-500","transition-colors",3,"ngModelChange","ngModel"],["for","email",1,"block","text-sm","font-medium","text-neutral-700","mb-2"],["type","email","id","email","name","email","required","","placeholder","<EMAIL>",1,"w-full","px-4","py-3","border","border-neutral-300","rounded-lg","focus:ring-2","focus:ring-primary-500","focus:border-primary-500","transition-colors",3,"ngModelChange","ngModel"],["for","phone",1,"block","text-sm","font-medium","text-neutral-700","mb-2"],["type","tel","id","phone","name","phone","placeholder","+****************",1,"w-full","px-4","py-3","border","border-neutral-300","rounded-lg","focus:ring-2","focus:ring-primary-500","focus:border-primary-500","transition-colors",3,"ngModelChange","ngModel"],["for","company",1,"block","text-sm","font-medium","text-neutral-700","mb-2"],["type","text","id","company","name","company","required","","placeholder","Acme Corporation",1,"w-full","px-4","py-3","border","border-neutral-300","rounded-lg","focus:ring-2","focus:ring-primary-500","focus:border-primary-500","transition-colors",3,"ngModelChange","ngModel"],["for","teamSize",1,"block","text-sm","font-medium","text-neutral-700","mb-2"],["id","teamSize","name","teamSize","required","",1,"w-full","px-4","py-3","border","border-neutral-300","rounded-lg","focus:ring-2","focus:ring-primary-500","focus:border-primary-500","transition-colors",3,"ngModelChange","ngModel"],["value",""],["value","1-10"],["value","11-50"],["value","51-200"],["value","201-1000"],["value","1000+"],["for","jobTitle",1,"block","text-sm","font-medium","text-neutral-700","mb-2"],["type","text","id","jobTitle","name","jobTitle","required","","placeholder","CTO, Project Manager, etc.",1,"w-full","px-4","py-3","border","border-neutral-300","rounded-lg","focus:ring-2","focus:ring-primary-500","focus:border-primary-500","transition-colors",3,"ngModelChange","ngModel"],["for","interest",1,"block","text-sm","font-medium","text-neutral-700","mb-2"],["id","interest","name","interest","required","",1,"w-full","px-4","py-3","border","border-neutral-300","rounded-lg","focus:ring-2","focus:ring-primary-500","focus:border-primary-500","transition-colors",3,"ngModelChange","ngModel"],["value","demo"],["value","pricing"],["value","integration"],["value","enterprise"],["value","migration"],["value","training"],["for","message",1,"block","text-sm","font-medium","text-neutral-700","mb-2"],["id","message","name","message","rows","4","placeholder","Describe your current challenges and how AI Hub might help your team...",1,"w-full","px-4","py-3","border","border-neutral-300","rounded-lg","focus:ring-2","focus:ring-primary-500","focus:border-primary-500","transition-colors","resize-none",3,"ngModelChange","ngModel"],[1,"pt-4"],["type","submit",1,"w-full","btn","btn-primary","btn-lg","disabled:opacity-50","disabled:cursor-not-allowed",3,"disabled"],["class","flex items-center justify-center",4,"ngIf"],[1,"text-xs","text-neutral-500","text-center"],["href","#privacy",1,"text-primary-600","hover:text-primary-700","underline"],[1,"space-y-8"],[1,"bg-gradient-to-br","from-primary-500","to-secondary-500","rounded-2xl","p-8","text-white"],[1,"text-2xl","font-bold","mb-6"],[1,"space-y-6"],[1,"flex","items-start","space-x-4"],[1,"w-12","h-12","bg-white/20","rounded-xl","flex","items-center","justify-center","flex-shrink-0"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6"],[1,"font-semibold","mb-1"],[1,"text-white/90","text-sm","mb-2"],["href","tel:******-AI-HUBS",1,"text-white","font-medium","hover:text-white/80","transition-colors"],[1,"text-white/70","text-xs","mt-1"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"],["href","mailto:<EMAIL>",1,"text-white","font-medium","hover:text-white/80","transition-colors"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"],[1,"text-white","font-medium","hover:text-white/80","transition-colors"],[1,"text-2xl","font-bold","text-neutral-900","mb-6"],[1,"space-y-4"],[1,"flex","items-start","space-x-3"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-primary-500","mt-0.5","flex-shrink-0"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M5 13l4 4L19 7"],[1,"font-semibold","text-neutral-900","mb-1"],[1,"text-neutral-600","text-sm"],[1,"bg-neutral-50","rounded-2xl","p-8"],[1,"text-lg","font-bold","text-neutral-900","mb-6","text-center"],[1,"grid","grid-cols-2","md:grid-cols-4","gap-6","items-center","opacity-60"],[1,"bg-neutral-200","rounded-lg","h-12","flex","items-center","justify-center"],[1,"text-neutral-500","font-semibold","text-sm"],[1,"mb-16"],[1,"text-center","mb-12"],[1,"text-3xl","font-bold","text-neutral-900","mb-4"],[1,"text-xl","text-neutral-600","max-w-2xl","mx-auto"],[1,"grid","md:grid-cols-4","gap-8"],[1,"text-center"],[1,"w-16","h-16","gradient-primary","rounded-2xl","flex","items-center","justify-center","mx-auto","mb-4"],[1,"text-2xl","font-bold","text-white"],[1,"text-lg","font-semibold","text-neutral-900","mb-2"],[1,"w-16","h-16","bg-secondary-500","rounded-2xl","flex","items-center","justify-center","mx-auto","mb-4"],[1,"w-16","h-16","bg-accent-400","rounded-2xl","flex","items-center","justify-center","mx-auto","mb-4"],[1,"w-16","h-16","bg-warning-400","rounded-2xl","flex","items-center","justify-center","mx-auto","mb-4"],[1,"max-w-4xl","mx-auto"],[1,"bg-white","rounded-xl","border","border-neutral-200","overflow-hidden"],[1,"w-full","px-6","py-4","text-left","flex","items-center","justify-between","hover:bg-neutral-50","transition-colors",3,"click"],[1,"font-semibold","text-neutral-900"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-neutral-500","transform","transition-transform"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 9l-7 7-7-7"],[1,"px-6","pb-4","text-neutral-600"],[1,"text-center","bg-gradient-to-r","from-primary-500","to-secondary-500","rounded-2xl","p-12","text-white"],[1,"text-3xl","font-bold","mb-4"],[1,"text-xl","text-white/90","mb-8","max-w-2xl","mx-auto"],[1,"flex","flex-col","sm:flex-row","gap-4","justify-center","mb-6"],["href","tel:******-AI-HUBS",1,"btn","bg-white","text-primary-600","hover:bg-gray-100","btn-lg"],[1,"btn","bg-white/20","text-white","border","border-white/30","hover:bg-white/30","btn-lg"],[1,"flex","items-center","justify-center","space-x-6","text-sm","text-white/80"],[1,"flex","items-center"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-4","h-4","mr-2","text-white"],[1,"flex","items-center","justify-center"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 19l9 2-9-18-9 18 9-2zm0 0v-8"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24",1,"animate-spin","-ml-1","mr-3","h-5","w-5","text-white"],["cx","12","cy","12","r","10","stroke","currentColor","stroke-width","4",1,"opacity-25"],["fill","currentColor","d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",1,"opacity-75"]],template:function(e,i){if(e&1){let s=Ne();r(0,"section",1)(1,"div",2)(2,"div",3)(3,"div",4),h(),r(4,"svg",5),u(5,"path",6),o(),a(6," Contact Sales Team "),o(),f(),r(7,"h2",7),a(8," Ready to Transform Your "),r(9,"span",8),a(10,"Business?"),o()(),r(11,"p",9),a(12," Connect with our AI experts to discover how AI Hub can revolutionize your team's productivity. Get personalized demos, custom pricing, and dedicated support for your organization. "),o()(),r(13,"div",10)(14,"div",11)(15,"div",12)(16,"h3",13),a(17,"Get Started Today"),o(),r(18,"p",14),a(19," Fill out the form below and our sales team will contact you within 24 hours to schedule a personalized demo. "),o()(),r(20,"form",15,0),C("ngSubmit",function(){return p(s),g(i.onSubmit())}),r(22,"div",16)(23,"div")(24,"label",17),a(25," First Name * "),o(),r(26,"input",18),M("ngModelChange",function(l){return p(s),S(i.formData.firstName,l)||(i.formData.firstName=l),g(l)}),o()(),r(27,"div")(28,"label",19),a(29," Last Name * "),o(),r(30,"input",20),M("ngModelChange",function(l){return p(s),S(i.formData.lastName,l)||(i.formData.lastName=l),g(l)}),o()()(),r(31,"div")(32,"label",21),a(33," Business Email * "),o(),r(34,"input",22),M("ngModelChange",function(l){return p(s),S(i.formData.email,l)||(i.formData.email=l),g(l)}),o()(),r(35,"div")(36,"label",23),a(37," Phone Number "),o(),r(38,"input",24),M("ngModelChange",function(l){return p(s),S(i.formData.phone,l)||(i.formData.phone=l),g(l)}),o()(),r(39,"div",16)(40,"div")(41,"label",25),a(42," Company Name * "),o(),r(43,"input",26),M("ngModelChange",function(l){return p(s),S(i.formData.company,l)||(i.formData.company=l),g(l)}),o()(),r(44,"div")(45,"label",27),a(46," Team Size * "),o(),r(47,"select",28),M("ngModelChange",function(l){return p(s),S(i.formData.teamSize,l)||(i.formData.teamSize=l),g(l)}),r(48,"option",29),a(49,"Select team size"),o(),r(50,"option",30),a(51,"1-10 employees"),o(),r(52,"option",31),a(53,"11-50 employees"),o(),r(54,"option",32),a(55,"51-200 employees"),o(),r(56,"option",33),a(57,"201-1000 employees"),o(),r(58,"option",34),a(59,"1000+ employees"),o()()()(),r(60,"div")(61,"label",35),a(62," Job Title * "),o(),r(63,"input",36),M("ngModelChange",function(l){return p(s),S(i.formData.jobTitle,l)||(i.formData.jobTitle=l),g(l)}),o()(),r(64,"div")(65,"label",37),a(66," Primary Interest * "),o(),r(67,"select",38),M("ngModelChange",function(l){return p(s),S(i.formData.interest,l)||(i.formData.interest=l),g(l)}),r(68,"option",29),a(69,"Select your primary interest"),o(),r(70,"option",39),a(71,"Schedule a Demo"),o(),r(72,"option",40),a(73,"Custom Pricing"),o(),r(74,"option",41),a(75,"Integration Support"),o(),r(76,"option",42),a(77,"Enterprise Solutions"),o(),r(78,"option",43),a(79,"Data Migration"),o(),r(80,"option",44),a(81,"Team Training"),o()()(),r(82,"div")(83,"label",45),a(84," Tell us about your needs "),o(),r(85,"textarea",46),M("ngModelChange",function(l){return p(s),S(i.formData.message,l)||(i.formData.message=l),g(l)}),o()(),r(86,"div",47)(87,"button",48),Ie(88,tn,4,0,"span",49)(89,nn,5,0,"span",49),o()(),r(90,"div",50),a(91," By submitting this form, you agree to our "),r(92,"a",51),a(93,"Privacy Policy"),o(),a(94," and consent to being contacted by our sales team. "),o()()(),r(95,"div",52)(96,"div",53)(97,"h3",54),a(98,"Get in Touch"),o(),r(99,"div",55)(100,"div",56)(101,"div",57),h(),r(102,"svg",58),u(103,"path",6),o()(),f(),r(104,"div")(105,"h4",59),a(106,"Sales Hotline"),o(),r(107,"p",60),a(108,"Speak directly with our sales experts"),o(),r(109,"a",61),a(110," +1 (800) AI-HUBS "),o(),r(111,"p",62),a(112,"Mon-Fri, 9 AM - 6 PM EST"),o()()(),r(113,"div",56)(114,"div",57),h(),r(115,"svg",58),u(116,"path",63),o()(),f(),r(117,"div")(118,"h4",59),a(119,"Sales Email"),o(),r(120,"p",60),a(121,"For detailed inquiries and proposals"),o(),r(122,"a",64),a(123," <EMAIL> "),o(),r(124,"p",62),a(125,"Response within 4 hours"),o()()(),r(126,"div",56)(127,"div",57),h(),r(128,"svg",58),u(129,"path",65),o()(),f(),r(130,"div")(131,"h4",59),a(132,"Live Chat"),o(),r(133,"p",60),a(134,"Instant support from our team"),o(),r(135,"button",66),a(136," Start Chat Now "),o(),r(137,"p",62),a(138,"Available 24/7"),o()()()()(),r(139,"div",11)(140,"h3",67),a(141,"Why Choose AI Hub?"),o(),r(142,"div",68)(143,"div",69),h(),r(144,"svg",70),u(145,"path",71),o(),f(),r(146,"div")(147,"h4",72),a(148,"300% Productivity Boost"),o(),r(149,"p",73),a(150,"Average improvement reported by our enterprise clients"),o()()(),r(151,"div",69),h(),r(152,"svg",70),u(153,"path",71),o(),f(),r(154,"div")(155,"h4",72),a(156,"Enterprise-Grade Security"),o(),r(157,"p",73),a(158,"SOC 2 Type II certified with end-to-end encryption"),o()()(),r(159,"div",69),h(),r(160,"svg",70),u(161,"path",71),o(),f(),r(162,"div")(163,"h4",72),a(164,"24/7 Dedicated Support"),o(),r(165,"p",73),a(166,"Priority support with dedicated customer success manager"),o()()(),r(167,"div",69),h(),r(168,"svg",70),u(169,"path",71),o(),f(),r(170,"div")(171,"h4",72),a(172,"Seamless Integration"),o(),r(173,"p",73),a(174,"Connect with 500+ tools including Slack, Microsoft Teams, and more"),o()()(),r(175,"div",69),h(),r(176,"svg",70),u(177,"path",71),o(),f(),r(178,"div")(179,"h4",72),a(180,"Custom Implementation"),o(),r(181,"p",73),a(182,"Tailored setup and training for your specific business needs"),o()()()()(),r(183,"div",74)(184,"h3",75),a(185,"Trusted by Industry Leaders"),o(),r(186,"div",76)(187,"div",77)(188,"span",78),a(189,"TechCorp"),o()(),r(190,"div",77)(191,"span",78),a(192,"GlobalTech"),o()(),r(193,"div",77)(194,"span",78),a(195,"StartupXYZ"),o()(),r(196,"div",77)(197,"span",78),a(198,"InnovateCo"),o()()()()()(),r(199,"div",79)(200,"div",80)(201,"h3",81),a(202,"Our Sales Process"),o(),r(203,"p",82),a(204," Simple, transparent, and designed to get you up and running quickly "),o()(),r(205,"div",83)(206,"div",84)(207,"div",85)(208,"span",86),a(209,"1"),o()(),r(210,"h4",87),a(211,"Initial Contact"),o(),r(212,"p",73),a(213," Fill out the form or call us. We'll schedule a discovery call within 24 hours. "),o()(),r(214,"div",84)(215,"div",88)(216,"span",86),a(217,"2"),o()(),r(218,"h4",87),a(219,"Personalized Demo"),o(),r(220,"p",73),a(221," See AI Hub in action with a demo tailored to your specific use cases and needs. "),o()(),r(222,"div",84)(223,"div",89)(224,"span",86),a(225,"3"),o()(),r(226,"h4",87),a(227,"Custom Proposal"),o(),r(228,"p",73),a(229," Receive a detailed proposal with pricing, implementation plan, and timeline. "),o()(),r(230,"div",84)(231,"div",90)(232,"span",86),a(233,"4"),o()(),r(234,"h4",87),a(235,"Onboarding"),o(),r(236,"p",73),a(237," Get started with dedicated support, training, and seamless data migration. "),o()()()(),r(238,"div",79)(239,"div",80)(240,"h3",81),a(241,"Frequently Asked Questions"),o(),r(242,"p",82),a(243," Get answers to common questions about AI Hub enterprise solutions "),o()(),r(244,"div",91)(245,"div",68)(246,"div",92)(247,"button",93),C("click",function(){return p(s),g(i.toggleFaq(0))}),r(248,"span",94),a(249,"How long does implementation take?"),o(),h(),r(250,"svg",95),u(251,"path",96),o()(),f(),r(252,"div",97)(253,"p"),a(254," Most implementations take 2-4 weeks depending on your team size and integration requirements. Our dedicated implementation team will work with you to ensure a smooth transition with minimal disruption to your workflow. "),o()()(),r(255,"div",92)(256,"button",93),C("click",function(){return p(s),g(i.toggleFaq(1))}),r(257,"span",94),a(258,"What's included in enterprise support?"),o(),h(),r(259,"svg",95),u(260,"path",96),o()(),f(),r(261,"div",97)(262,"p"),a(263," Enterprise support includes 24/7 priority support, dedicated customer success manager, custom training sessions, API support, and guaranteed 99.9% uptime SLA with priority incident response. "),o()()(),r(264,"div",92)(265,"button",93),C("click",function(){return p(s),g(i.toggleFaq(2))}),r(266,"span",94),a(267,"Can we integrate with our existing tools?"),o(),h(),r(268,"svg",95),u(269,"path",96),o()(),f(),r(270,"div",97)(271,"p"),a(272," Yes! AI Hub integrates with 500+ popular tools including Slack, Microsoft Teams, Jira, Salesforce, Google Workspace, and more. We also provide REST APIs and webhooks for custom integrations. "),o()()(),r(273,"div",92)(274,"button",93),C("click",function(){return p(s),g(i.toggleFaq(3))}),r(275,"span",94),a(276,"What security measures are in place?"),o(),h(),r(277,"svg",95),u(278,"path",96),o()(),f(),r(279,"div",97)(280,"p"),a(281," AI Hub is SOC 2 Type II certified with enterprise-grade security including end-to-end encryption, SSO integration, role-based access controls, audit logs, and compliance with GDPR, HIPAA, and other regulations. "),o()()(),r(282,"div",92)(283,"button",93),C("click",function(){return p(s),g(i.toggleFaq(4))}),r(284,"span",94),a(285,"Do you offer custom pricing for large teams?"),o(),h(),r(286,"svg",95),u(287,"path",96),o()(),f(),r(288,"div",97)(289,"p"),a(290," Absolutely! We offer volume discounts and custom pricing for teams of 100+ users. Contact our sales team to discuss your specific needs and get a personalized quote. "),o()()()()()(),r(291,"div",98)(292,"h3",99),a(293,"Ready to Get Started?"),o(),r(294,"p",100),a(295," Join thousands of teams already using AI Hub to transform their productivity. Our sales team is standing by to help you get started. "),o(),r(296,"div",101)(297,"a",102),h(),r(298,"svg",5),u(299,"path",6),o(),a(300," Call Sales Now "),o(),f(),r(301,"button",103),h(),r(302,"svg",5),u(303,"path",65),o(),a(304," Start Live Chat "),o()(),f(),r(305,"div",104)(306,"div",105),h(),r(307,"svg",106),u(308,"path",71),o(),a(309," Free consultation "),o(),f(),r(310,"div",105),h(),r(311,"svg",106),u(312,"path",71),o(),a(313," Custom demo "),o(),f(),r(314,"div",105),h(),r(315,"svg",106),u(316,"path",71),o(),a(317," No commitment "),o()()()()()}if(e&2){let s=ke(21);c(26),w("ngModel",i.formData.firstName),c(4),w("ngModel",i.formData.lastName),c(4),w("ngModel",i.formData.email),c(4),w("ngModel",i.formData.phone),c(5),w("ngModel",i.formData.company),c(4),w("ngModel",i.formData.teamSize),c(16),w("ngModel",i.formData.jobTitle),c(4),w("ngModel",i.formData.interest),c(18),w("ngModel",i.formData.message),c(2),J("disabled",!s.form.valid||i.isSubmitting),c(),J("ngIf",!i.isSubmitting),c(),J("ngIf",i.isSubmitting),c(161),_("rotate-180",i.openFaqIndex===0),c(2),_("hidden",i.openFaqIndex!==0),c(7),_("rotate-180",i.openFaqIndex===1),c(2),_("hidden",i.openFaqIndex!==1),c(7),_("rotate-180",i.openFaqIndex===2),c(2),_("hidden",i.openFaqIndex!==2),c(7),_("rotate-180",i.openFaqIndex===3),c(2),_("hidden",i.openFaqIndex!==3),c(7),_("rotate-180",i.openFaqIndex===4),c(2),_("hidden",i.openFaqIndex!==4)}},dependencies:[je,Ge,pt,ut,mt,ft,oe,ae,it,rt,xe,Me,Se],styles:['.gradient-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6,#8b5cf6)}.form-input[_ngcontent-%COMP%]{transition:all .3s ease}.form-input[_ngcontent-%COMP%]:focus{transform:translateY(-1px);box-shadow:0 4px 12px #3b82f626}.btn-submit[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s ease}.btn-submit[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.btn-submit[_ngcontent-%COMP%]:hover:before{left:100%}.btn-submit[_ngcontent-%COMP%]:disabled{cursor:not-allowed;opacity:.6}.contact-method-card[_ngcontent-%COMP%]{transition:all .3s ease;position:relative;overflow:hidden}.contact-method-card[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background:linear-gradient(135deg,#ffffff1a,#ffffff0d);opacity:0;transition:opacity .3s ease}.contact-method-card[_ngcontent-%COMP%]:hover:before{opacity:1}.contact-method-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.benefit-item[_ngcontent-%COMP%]{transition:all .3s ease}.benefit-item[_ngcontent-%COMP%]:hover{transform:translate(4px)}.benefit-item[_ngcontent-%COMP%]:hover   .benefit-icon[_ngcontent-%COMP%]{transform:scale(1.1)}.benefit-icon[_ngcontent-%COMP%]{transition:transform .3s ease}.sales-step[_ngcontent-%COMP%]{transition:all .3s ease}.sales-step[_ngcontent-%COMP%]:hover{transform:translateY(-4px)}.sales-step-number[_ngcontent-%COMP%]{transition:all .3s ease}.sales-step[_ngcontent-%COMP%]:hover   .sales-step-number[_ngcontent-%COMP%]{transform:scale(1.1)}.faq-item[_ngcontent-%COMP%]{transition:all .3s ease}.faq-item[_ngcontent-%COMP%]:hover{box-shadow:0 4px 12px #0000001a}.faq-button[_ngcontent-%COMP%]{transition:all .2s ease}.faq-button[_ngcontent-%COMP%]:hover{background-color:#f9fafb}.faq-icon[_ngcontent-%COMP%]{transition:transform .3s ease}.faq-content[_ngcontent-%COMP%]{transition:all .3s ease;max-height:0;overflow:hidden}.faq-content.open[_ngcontent-%COMP%]{max-height:200px;padding-bottom:1rem}.company-logo[_ngcontent-%COMP%]{transition:all .3s ease;filter:grayscale(100%)}.company-logo[_ngcontent-%COMP%]:hover{filter:grayscale(0%);transform:scale(1.05)}.spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.success-message[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInUp .5s ease-out}.error-message[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_shake .5s ease-in-out}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_shake{0%,to{transform:translate(0)}25%{transform:translate(-5px)}75%{transform:translate(5px)}}.form-field.invalid[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-field.invalid[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], .form-field.invalid[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{border-color:#ef4444;box-shadow:0 0 0 3px #ef44441a}.form-field.valid[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-field.valid[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], .form-field.valid[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{border-color:#10b981;box-shadow:0 0 0 3px #10b9811a}@media (max-width: 768px){.sales-step[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.contact-method-card[_ngcontent-%COMP%]:hover{transform:translateY(-1px)}.benefit-item[_ngcontent-%COMP%]:hover{transform:translate(2px)}}.form-input[_ngcontent-%COMP%]:focus, .faq-button[_ngcontent-%COMP%]:focus{outline:2px solid #3b82f6;outline-offset:2px}@media print{.contact-sales-section[_ngcontent-%COMP%]{background:#fff!important}.gradient-primary[_ngcontent-%COMP%], .bg-gradient-to-br[_ngcontent-%COMP%]{background:#3b82f6!important;color:#fff!important}}@media (prefers-contrast: high){.form-input[_ngcontent-%COMP%], .btn-submit[_ngcontent-%COMP%]{border-width:2px}}@media (prefers-reduced-motion: reduce){.sales-step[_ngcontent-%COMP%], .contact-method-card[_ngcontent-%COMP%], .benefit-item[_ngcontent-%COMP%], .faq-item[_ngcontent-%COMP%]{transition:none}.sales-step[_ngcontent-%COMP%]:hover, .contact-method-card[_ngcontent-%COMP%]:hover, .benefit-item[_ngcontent-%COMP%]:hover{transform:none}}']})};var gt=class n{static \u0275fac=function(e){return new(e||n)};static \u0275cmp=Z({type:n,selectors:[["app-contact"]],decls:16,vars:0,consts:[[1,"min-h-screen","bg-neutral-50"],[1,"relative","overflow-hidden"],[1,"absolute","inset-0","bg-gradient-to-r","from-accent-400","via-secondary-500","to-primary-500"],[1,"absolute","inset-0","bg-gradient-to-br","from-transparent","via-primary-500/20","to-primary-600/40"],[1,"absolute","inset-0","overflow-hidden"],[1,"absolute","-top-40","-right-40","w-80","h-80","bg-white/10","rounded-full","blur-3xl","animate-pulse"],[1,"absolute","-bottom-40","-left-40","w-80","h-80","bg-white/5","rounded-full","blur-3xl","animate-pulse",2,"animation-delay","2s"],[1,"absolute","top-1/2","left-1/2","transform","-translate-x-1/2","-translate-y-1/2","w-96","h-96","bg-white/5","rounded-full","blur-3xl","animate-pulse",2,"animation-delay","4s"],[1,"relative","container-custom","py-24","md:py-32"],[1,"max-w-4xl","mx-auto","text-center"],[1,"text-5xl","md:text-6xl","lg:text-7xl","font-bold","text-white","mb-8","leading-tight"],[1,"text-xl","md:text-2xl","text-white/90","leading-relaxed","max-w-3xl","mx-auto"]],template:function(e,i){e&1&&(r(0,"div",0)(1,"section",1),u(2,"div",2)(3,"div",3),r(4,"div",4),u(5,"div",5)(6,"div",6)(7,"div",7),o(),r(8,"div",8)(9,"div",9)(10,"h1",10),a(11," Contact Sales Team "),o(),r(12,"p",11),a(13," Ready to transform your business with AI Hub? Get in touch with our sales team for personalized solutions and enterprise pricing. "),o()()()(),u(14,"app-contact-sales")(15,"app-cta-section"),o())},dependencies:[le,Re],encapsulation:2})};export{gt as ContactComponent};
