/* Contact Sales Section Styles */

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}

/* Form styles */
.form-input {
  transition: all 0.3s ease;
}

.form-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Button hover effects */
.btn-submit {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-submit::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-submit:hover::before {
  left: 100%;
}

.btn-submit:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Contact method cards */
.contact-method-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-method-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.contact-method-card:hover::before {
  opacity: 1;
}

.contact-method-card:hover {
  transform: translateY(-2px);
}

/* Benefits list */
.benefit-item {
  transition: all 0.3s ease;
}

.benefit-item:hover {
  transform: translateX(4px);
}

.benefit-item:hover .benefit-icon {
  transform: scale(1.1);
}

.benefit-icon {
  transition: transform 0.3s ease;
}

/* Sales process steps */
.sales-step {
  transition: all 0.3s ease;
}

.sales-step:hover {
  transform: translateY(-4px);
}

.sales-step-number {
  transition: all 0.3s ease;
}

.sales-step:hover .sales-step-number {
  transform: scale(1.1);
}

/* FAQ styles */
.faq-item {
  transition: all 0.3s ease;
}

.faq-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.faq-button {
  transition: all 0.2s ease;
}

.faq-button:hover {
  background-color: #f9fafb;
}

.faq-icon {
  transition: transform 0.3s ease;
}

.faq-content {
  transition: all 0.3s ease;
  max-height: 0;
  overflow: hidden;
}

.faq-content.open {
  max-height: 200px;
  padding-bottom: 1rem;
}

/* Company logos */
.company-logo {
  transition: all 0.3s ease;
  filter: grayscale(100%);
}

.company-logo:hover {
  filter: grayscale(0%);
  transform: scale(1.05);
}

/* Loading spinner */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Success/Error messages */
.success-message {
  animation: slideInUp 0.5s ease-out;
}

.error-message {
  animation: shake 0.5s ease-in-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Form validation styles */
.form-field.invalid input,
.form-field.invalid select,
.form-field.invalid textarea {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-field.valid input,
.form-field.valid select,
.form-field.valid textarea {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sales-step:hover {
    transform: translateY(-2px);
  }

  .contact-method-card:hover {
    transform: translateY(-1px);
  }

  .benefit-item:hover {
    transform: translateX(2px);
  }
}

/* Focus styles for accessibility */
.form-input:focus,
.faq-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .contact-sales-section {
    background: white !important;
  }

  .gradient-primary,
  .bg-gradient-to-br {
    background: #3b82f6 !important;
    color: white !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .form-input {
    border-width: 2px;
  }

  .btn-submit {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .sales-step,
  .contact-method-card,
  .benefit-item,
  .faq-item {
    transition: none;
  }

  .sales-step:hover,
  .contact-method-card:hover,
  .benefit-item:hover {
    transform: none;
  }
}
