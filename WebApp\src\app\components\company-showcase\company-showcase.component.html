<!-- Company Showcase Section -->
<section class="py-20 bg-gradient-to-br from-neutral-50 to-white">
  <div class="container-custom">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <div class="inline-flex items-center px-4 py-2 bg-primary-100 rounded-full text-primary-700 text-sm font-medium mb-4">
        <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-2">
          <span class="text-white font-bold text-xs">IT</span>
        </div>
        About Our Company
      </div>
      <h2 class="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
        Meet <span class="text-primary-500">IzonTech Solutions</span>
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto">
        A pioneering technology company with 15+ years of excellence in delivering innovative software solutions
        that transform businesses worldwide.
      </p>
    </div>

    <!-- Company Stats -->
    <div class="grid md:grid-cols-4 gap-8 mb-16">
      <div class="text-center">
        <div class="w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="text-3xl font-bold text-neutral-900 mb-2">15+</div>
        <div class="text-neutral-600">Years of Experience</div>
      </div>

      <div class="text-center">
        <div class="w-16 h-16 bg-secondary-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
        </div>
        <div class="text-3xl font-bold text-neutral-900 mb-2">500+</div>
        <div class="text-neutral-600">Projects Delivered</div>
      </div>

      <div class="text-center">
        <div class="w-16 h-16 bg-accent-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
        </div>
        <div class="text-3xl font-bold text-neutral-900 mb-2">200+</div>
        <div class="text-neutral-600">Happy Clients</div>
      </div>

      <div class="text-center">
        <div class="w-16 h-16 bg-warning-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="text-3xl font-bold text-neutral-900 mb-2">25+</div>
        <div class="text-neutral-600">Countries Served</div>
      </div>
    </div>

    <!-- Company Services -->
    <div class="grid lg:grid-cols-2 gap-12 items-center mb-16">
      <!-- Left Column - Services -->
      <div>
        <h3 class="text-3xl font-bold text-neutral-900 mb-8">Our Core Services</h3>

        <div class="space-y-6">
          <!-- AI/ML Solutions -->
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            </div>
            <div>
              <h4 class="text-xl font-semibold text-neutral-900 mb-2">AI & Machine Learning</h4>
              <p class="text-neutral-600">Advanced AI solutions, chatbots, predictive analytics, and intelligent automation systems.</p>
            </div>
          </div>

          <!-- Cloud Computing -->
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-secondary-100 rounded-xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"></path>
              </svg>
            </div>
            <div>
              <h4 class="text-xl font-semibold text-neutral-900 mb-2">Cloud Computing</h4>
              <p class="text-neutral-600">AWS, Azure, Google Cloud solutions, migration services, and cloud-native application development.</p>
            </div>
          </div>

          <!-- Enterprise Software -->
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-accent-100 rounded-xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
            <div>
              <h4 class="text-xl font-semibold text-neutral-900 mb-2">Enterprise Software</h4>
              <p class="text-neutral-600">Custom ERP, CRM, project management, and business process automation solutions.</p>
            </div>
          </div>

          <!-- Digital Transformation -->
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-warning-100 rounded-xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <div>
              <h4 class="text-xl font-semibold text-neutral-900 mb-2">Digital Transformation</h4>
              <p class="text-neutral-600">Complete digital transformation consulting, modernization, and technology strategy services.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column - Company Image/Visual -->
      <div class="relative">
        <div class="bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl p-8 text-white">
          <div class="text-center">
            <div class="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <span class="text-3xl font-bold">IT</span>
            </div>
            <h4 class="text-2xl font-bold mb-4">IzonTech Solutions</h4>
            <p class="text-white/90 mb-6">
              "Innovation Through Technology" - Our mission is to empower businesses with cutting-edge technology solutions
              that drive growth, efficiency, and competitive advantage.
            </p>

            <!-- Company Highlights -->
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div class="bg-white/10 rounded-lg p-3">
                <div class="font-semibold">Founded</div>
                <div class="text-white/80">2008</div>
              </div>
              <div class="bg-white/10 rounded-lg p-3">
                <div class="font-semibold">Headquarters</div>
                <div class="text-white/80">Global</div>
              </div>
              <div class="bg-white/10 rounded-lg p-3">
                <div class="font-semibold">Team Size</div>
                <div class="text-white/80">100+ Experts</div>
              </div>
              <div class="bg-white/10 rounded-lg p-3">
                <div class="font-semibold">Specialization</div>
                <div class="text-white/80">AI & Cloud</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Our Portfolio/Works -->
    <div class="mb-16">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-neutral-900 mb-4">Our Notable Works</h3>
        <p class="text-xl text-neutral-600 max-w-2xl mx-auto">
          Showcasing some of our flagship projects and innovative solutions that have transformed businesses
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- AI Hub Project -->
        <div class="card p-6 bg-white border border-neutral-200 hover-lift">
          <div class="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-3">AI Hub Platform</h4>
          <p class="text-neutral-600 text-sm mb-4">
            Our flagship AI-powered workspace management platform that revolutionizes how teams collaborate and manage projects.
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">AI/ML</span>
            <span class="px-2 py-1 bg-secondary-100 text-secondary-700 text-xs rounded-full">Angular</span>
            <span class="px-2 py-1 bg-accent-100 text-accent-700 text-xs rounded-full">Cloud</span>
          </div>
          <div class="text-primary-500 font-medium text-sm">Current Project</div>
        </div>

        <!-- Enterprise ERP -->
        <div class="card p-6 bg-white border border-neutral-200 hover-lift">
          <div class="w-12 h-12 bg-secondary-500 rounded-xl flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-3">Enterprise ERP Suite</h4>
          <p class="text-neutral-600 text-sm mb-4">
            Comprehensive enterprise resource planning solution serving 50+ multinational corporations worldwide.
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="px-2 py-1 bg-secondary-100 text-secondary-700 text-xs rounded-full">.NET</span>
            <span class="px-2 py-1 bg-accent-100 text-accent-700 text-xs rounded-full">SQL Server</span>
            <span class="px-2 py-1 bg-warning-100 text-warning-700 text-xs rounded-full">Azure</span>
          </div>
          <div class="text-secondary-500 font-medium text-sm">Enterprise Solution</div>
        </div>

        <!-- Smart Analytics -->
        <div class="card p-6 bg-white border border-neutral-200 hover-lift">
          <div class="w-12 h-12 bg-accent-400 rounded-xl flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-3">Smart Analytics Platform</h4>
          <p class="text-neutral-600 text-sm mb-4">
            Advanced business intelligence and predictive analytics platform with real-time data processing capabilities.
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="px-2 py-1 bg-accent-100 text-accent-700 text-xs rounded-full">Python</span>
            <span class="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">TensorFlow</span>
            <span class="px-2 py-1 bg-warning-100 text-warning-700 text-xs rounded-full">AWS</span>
          </div>
          <div class="text-accent-500 font-medium text-sm">AI Analytics</div>
        </div>

        <!-- Cloud Migration -->
        <div class="card p-6 bg-white border border-neutral-200 hover-lift">
          <div class="w-12 h-12 bg-warning-400 rounded-xl flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"></path>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-3">Cloud Migration Services</h4>
          <p class="text-neutral-600 text-sm mb-4">
            Successfully migrated 200+ enterprise applications to cloud platforms with zero downtime and enhanced performance.
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="px-2 py-1 bg-warning-100 text-warning-700 text-xs rounded-full">AWS</span>
            <span class="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">Azure</span>
            <span class="px-2 py-1 bg-secondary-100 text-secondary-700 text-xs rounded-full">GCP</span>
          </div>
          <div class="text-warning-500 font-medium text-sm">Cloud Services</div>
        </div>

        <!-- Mobile Solutions -->
        <div class="card p-6 bg-white border border-neutral-200 hover-lift">
          <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-3">Mobile App Development</h4>
          <p class="text-neutral-600 text-sm mb-4">
            Cross-platform mobile applications with native performance, serving millions of users globally.
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">React Native</span>
            <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">Flutter</span>
            <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">Native</span>
          </div>
          <div class="text-purple-500 font-medium text-sm">Mobile Solutions</div>
        </div>

        <!-- Blockchain -->
        <div class="card p-6 bg-white border border-neutral-200 hover-lift">
          <div class="w-12 h-12 bg-indigo-500 rounded-xl flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-neutral-900 mb-3">Blockchain Solutions</h4>
          <p class="text-neutral-600 text-sm mb-4">
            Secure blockchain applications, smart contracts, and decentralized solutions for various industries.
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full">Ethereum</span>
            <span class="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full">Solidity</span>
            <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">Web3</span>
          </div>
          <div class="text-indigo-500 font-medium text-sm">Blockchain</div>
        </div>
      </div>
    </div>

    <!-- Call to Action -->
    <div class="text-center">
      <div class="bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 text-white">
        <h3 class="text-2xl font-bold mb-4">Ready to Transform Your Business?</h3>
        <p class="text-white/90 mb-6 max-w-2xl mx-auto">
          Partner with IzonTech Solutions and leverage our 15+ years of expertise to build innovative solutions
          that drive your business forward.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="https://izontechsolution.com/" target="_blank" rel="noopener noreferrer"
             class="btn bg-white text-primary-600 hover:bg-gray-100">
            Visit Our Website
          </a>
          <a href="#contact" class="btn bg-white/20 text-white border border-white/30 hover:bg-white/30">
            Get In Touch
          </a>
        </div>
      </div>
    </div>
  </div>
</section>
