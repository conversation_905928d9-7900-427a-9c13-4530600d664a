<!-- Contact Sales Section -->
<section id="contact-sales" class="py-20 bg-gradient-to-br from-neutral-50 to-white">
  <div class="container-custom">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <div class="inline-flex items-center px-4 py-2 bg-primary-100 rounded-full text-primary-700 text-sm font-medium mb-6">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21L6.16 11.37a11.045 11.045 0 005.516 5.516l1.983-4.064a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
        </svg>
        Contact Sales Team
      </div>
      <h2 class="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
        Ready to Transform Your <span class="text-primary-500">Business?</span>
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
        Connect with our AI experts to discover how AI Hub can revolutionize your team's productivity.
        Get personalized demos, custom pricing, and dedicated support for your organization.
      </p>
    </div>

    <!-- Main Content Grid -->
    <div class="grid lg:grid-cols-2 gap-12 mb-16">
      <!-- Contact Form -->
      <div class="bg-white rounded-2xl p-8 shadow-lg border border-neutral-100">
        <div class="mb-8">
          <h3 class="text-2xl font-bold text-neutral-900 mb-4">Get Started Today</h3>
          <p class="text-neutral-600">
            Fill out the form below and our sales team will contact you within 24 hours to schedule a personalized demo.
          </p>
        </div>

        <form class="space-y-6" (ngSubmit)="onSubmit()" #contactForm="ngForm">
          <!-- Name Fields -->
          <div class="grid md:grid-cols-2 gap-4">
            <div>
              <label for="firstName" class="block text-sm font-medium text-neutral-700 mb-2">
                First Name *
              </label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                [(ngModel)]="formData.firstName"
                required
                class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                placeholder="John"
              >
            </div>
            <div>
              <label for="lastName" class="block text-sm font-medium text-neutral-700 mb-2">
                Last Name *
              </label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                [(ngModel)]="formData.lastName"
                required
                class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                placeholder="Doe"
              >
            </div>
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-neutral-700 mb-2">
              Business Email *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              [(ngModel)]="formData.email"
              required
              class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              placeholder="<EMAIL>"
            >
          </div>

          <!-- Phone -->
          <div>
            <label for="phone" class="block text-sm font-medium text-neutral-700 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              [(ngModel)]="formData.phone"
              class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              placeholder="+****************"
            >
          </div>

          <!-- Company Info -->
          <div class="grid md:grid-cols-2 gap-4">
            <div>
              <label for="company" class="block text-sm font-medium text-neutral-700 mb-2">
                Company Name *
              </label>
              <input
                type="text"
                id="company"
                name="company"
                [(ngModel)]="formData.company"
                required
                class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                placeholder="Acme Corporation"
              >
            </div>
            <div>
              <label for="teamSize" class="block text-sm font-medium text-neutral-700 mb-2">
                Team Size *
              </label>
              <select
                id="teamSize"
                name="teamSize"
                [(ngModel)]="formData.teamSize"
                required
                class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              >
                <option value="">Select team size</option>
                <option value="1-10">1-10 employees</option>
                <option value="11-50">11-50 employees</option>
                <option value="51-200">51-200 employees</option>
                <option value="201-1000">201-1000 employees</option>
                <option value="1000+">1000+ employees</option>
              </select>
            </div>
          </div>

          <!-- Job Title -->
          <div>
            <label for="jobTitle" class="block text-sm font-medium text-neutral-700 mb-2">
              Job Title *
            </label>
            <input
              type="text"
              id="jobTitle"
              name="jobTitle"
              [(ngModel)]="formData.jobTitle"
              required
              class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              placeholder="CTO, Project Manager, etc."
            >
          </div>

          <!-- Interest -->
          <div>
            <label for="interest" class="block text-sm font-medium text-neutral-700 mb-2">
              Primary Interest *
            </label>
            <select
              id="interest"
              name="interest"
              [(ngModel)]="formData.interest"
              required
              class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            >
              <option value="">Select your primary interest</option>
              <option value="demo">Schedule a Demo</option>
              <option value="pricing">Custom Pricing</option>
              <option value="integration">Integration Support</option>
              <option value="enterprise">Enterprise Solutions</option>
              <option value="migration">Data Migration</option>
              <option value="training">Team Training</option>
            </select>
          </div>

          <!-- Message -->
          <div>
            <label for="message" class="block text-sm font-medium text-neutral-700 mb-2">
              Tell us about your needs
            </label>
            <textarea
              id="message"
              name="message"
              [(ngModel)]="formData.message"
              rows="4"
              class="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors resize-none"
              placeholder="Describe your current challenges and how AI Hub might help your team..."
            ></textarea>
          </div>

          <!-- Submit Button -->
          <div class="pt-4">
            <button
              type="submit"
              [disabled]="!contactForm.form.valid || isSubmitting"
              class="w-full btn btn-primary btn-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span *ngIf="!isSubmitting" class="flex items-center justify-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                </svg>
                Contact Sales Team
              </span>
              <span *ngIf="isSubmitting" class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </span>
            </button>
          </div>

          <!-- Privacy Notice -->
          <div class="text-xs text-neutral-500 text-center">
            By submitting this form, you agree to our
            <a href="#privacy" class="text-primary-600 hover:text-primary-700 underline">Privacy Policy</a>
            and consent to being contacted by our sales team.
          </div>
        </form>
      </div>

      <!-- Contact Information & Benefits -->
      <div class="space-y-8">
        <!-- Contact Info -->
        <div class="bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl p-8 text-white">
          <h3 class="text-2xl font-bold mb-6">Get in Touch</h3>

          <div class="space-y-6">
            <!-- Phone -->
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21L6.16 11.37a11.045 11.045 0 005.516 5.516l1.983-4.064a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold mb-1">Sales Hotline</h4>
                <p class="text-white/90 text-sm mb-2">Speak directly with our sales experts</p>
                <a href="tel:******-AI-HUBS" class="text-white font-medium hover:text-white/80 transition-colors">
                  +1 (800) AI-HUBS
                </a>
                <p class="text-white/70 text-xs mt-1">Mon-Fri, 9 AM - 6 PM EST</p>
              </div>
            </div>

            <!-- Email -->
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold mb-1">Sales Email</h4>
                <p class="text-white/90 text-sm mb-2">For detailed inquiries and proposals</p>
                <a href="mailto:sales&#64;aihub.com" class="text-white font-medium hover:text-white/80 transition-colors">
                  sales&#64;aihub.com
                </a>
                <p class="text-white/70 text-xs mt-1">Response within 4 hours</p>
              </div>
            </div>

            <!-- Live Chat -->
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold mb-1">Live Chat</h4>
                <p class="text-white/90 text-sm mb-2">Instant support from our team</p>
                <button class="text-white font-medium hover:text-white/80 transition-colors">
                  Start Chat Now
                </button>
                <p class="text-white/70 text-xs mt-1">Available 24/7</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Why Choose AI Hub -->
        <div class="bg-white rounded-2xl p-8 shadow-lg border border-neutral-100">
          <h3 class="text-2xl font-bold text-neutral-900 mb-6">Why Choose AI Hub?</h3>

          <div class="space-y-4">
            <div class="flex items-start space-x-3">
              <svg class="w-6 h-6 text-primary-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <div>
                <h4 class="font-semibold text-neutral-900 mb-1">300% Productivity Boost</h4>
                <p class="text-neutral-600 text-sm">Average improvement reported by our enterprise clients</p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <svg class="w-6 h-6 text-primary-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <div>
                <h4 class="font-semibold text-neutral-900 mb-1">Enterprise-Grade Security</h4>
                <p class="text-neutral-600 text-sm">SOC 2 Type II certified with end-to-end encryption</p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <svg class="w-6 h-6 text-primary-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <div>
                <h4 class="font-semibold text-neutral-900 mb-1">24/7 Dedicated Support</h4>
                <p class="text-neutral-600 text-sm">Priority support with dedicated customer success manager</p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <svg class="w-6 h-6 text-primary-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <div>
                <h4 class="font-semibold text-neutral-900 mb-1">Seamless Integration</h4>
                <p class="text-neutral-600 text-sm">Connect with 500+ tools including Slack, Microsoft Teams, and more</p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <svg class="w-6 h-6 text-primary-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <div>
                <h4 class="font-semibold text-neutral-900 mb-1">Custom Implementation</h4>
                <p class="text-neutral-600 text-sm">Tailored setup and training for your specific business needs</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Trusted By -->
        <div class="bg-neutral-50 rounded-2xl p-8">
          <h3 class="text-lg font-bold text-neutral-900 mb-6 text-center">Trusted by Industry Leaders</h3>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-6 items-center opacity-60">
            <!-- Company Logos Placeholder -->
            <div class="bg-neutral-200 rounded-lg h-12 flex items-center justify-center">
              <span class="text-neutral-500 font-semibold text-sm">TechCorp</span>
            </div>
            <div class="bg-neutral-200 rounded-lg h-12 flex items-center justify-center">
              <span class="text-neutral-500 font-semibold text-sm">GlobalTech</span>
            </div>
            <div class="bg-neutral-200 rounded-lg h-12 flex items-center justify-center">
              <span class="text-neutral-500 font-semibold text-sm">StartupXYZ</span>
            </div>
            <div class="bg-neutral-200 rounded-lg h-12 flex items-center justify-center">
              <span class="text-neutral-500 font-semibold text-sm">InnovateCo</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sales Process -->
    <div class="mb-16">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-neutral-900 mb-4">Our Sales Process</h3>
        <p class="text-xl text-neutral-600 max-w-2xl mx-auto">
          Simple, transparent, and designed to get you up and running quickly
        </p>
      </div>

      <div class="grid md:grid-cols-4 gap-8">
        <!-- Step 1 -->
        <div class="text-center">
          <div class="w-16 h-16 gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl font-bold text-white">1</span>
          </div>
          <h4 class="text-lg font-semibold text-neutral-900 mb-2">Initial Contact</h4>
          <p class="text-neutral-600 text-sm">
            Fill out the form or call us. We'll schedule a discovery call within 24 hours.
          </p>
        </div>

        <!-- Step 2 -->
        <div class="text-center">
          <div class="w-16 h-16 bg-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl font-bold text-white">2</span>
          </div>
          <h4 class="text-lg font-semibold text-neutral-900 mb-2">Personalized Demo</h4>
          <p class="text-neutral-600 text-sm">
            See AI Hub in action with a demo tailored to your specific use cases and needs.
          </p>
        </div>

        <!-- Step 3 -->
        <div class="text-center">
          <div class="w-16 h-16 bg-accent-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl font-bold text-white">3</span>
          </div>
          <h4 class="text-lg font-semibold text-neutral-900 mb-2">Custom Proposal</h4>
          <p class="text-neutral-600 text-sm">
            Receive a detailed proposal with pricing, implementation plan, and timeline.
          </p>
        </div>

        <!-- Step 4 -->
        <div class="text-center">
          <div class="w-16 h-16 bg-warning-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl font-bold text-white">4</span>
          </div>
          <h4 class="text-lg font-semibold text-neutral-900 mb-2">Onboarding</h4>
          <p class="text-neutral-600 text-sm">
            Get started with dedicated support, training, and seamless data migration.
          </p>
        </div>
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="mb-16">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-bold text-neutral-900 mb-4">Frequently Asked Questions</h3>
        <p class="text-xl text-neutral-600 max-w-2xl mx-auto">
          Get answers to common questions about AI Hub enterprise solutions
        </p>
      </div>

      <div class="max-w-4xl mx-auto">
        <div class="space-y-4">
          <!-- FAQ Item 1 -->
          <div class="bg-white rounded-xl border border-neutral-200 overflow-hidden">
            <button
              class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-neutral-50 transition-colors"
              (click)="toggleFaq(0)"
            >
              <span class="font-semibold text-neutral-900">How long does implementation take?</span>
              <svg
                class="w-5 h-5 text-neutral-500 transform transition-transform"
                [class.rotate-180]="openFaqIndex === 0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div
              class="px-6 pb-4 text-neutral-600"
              [class.hidden]="openFaqIndex !== 0"
            >
              <p>
                Most implementations take 2-4 weeks depending on your team size and integration requirements.
                Our dedicated implementation team will work with you to ensure a smooth transition with minimal disruption to your workflow.
              </p>
            </div>
          </div>

          <!-- FAQ Item 2 -->
          <div class="bg-white rounded-xl border border-neutral-200 overflow-hidden">
            <button
              class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-neutral-50 transition-colors"
              (click)="toggleFaq(1)"
            >
              <span class="font-semibold text-neutral-900">What's included in enterprise support?</span>
              <svg
                class="w-5 h-5 text-neutral-500 transform transition-transform"
                [class.rotate-180]="openFaqIndex === 1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div
              class="px-6 pb-4 text-neutral-600"
              [class.hidden]="openFaqIndex !== 1"
            >
              <p>
                Enterprise support includes 24/7 priority support, dedicated customer success manager,
                custom training sessions, API support, and guaranteed 99.9% uptime SLA with priority incident response.
              </p>
            </div>
          </div>

          <!-- FAQ Item 3 -->
          <div class="bg-white rounded-xl border border-neutral-200 overflow-hidden">
            <button
              class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-neutral-50 transition-colors"
              (click)="toggleFaq(2)"
            >
              <span class="font-semibold text-neutral-900">Can we integrate with our existing tools?</span>
              <svg
                class="w-5 h-5 text-neutral-500 transform transition-transform"
                [class.rotate-180]="openFaqIndex === 2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div
              class="px-6 pb-4 text-neutral-600"
              [class.hidden]="openFaqIndex !== 2"
            >
              <p>
                Yes! AI Hub integrates with 500+ popular tools including Slack, Microsoft Teams, Jira, Salesforce,
                Google Workspace, and more. We also provide REST APIs and webhooks for custom integrations.
              </p>
            </div>
          </div>

          <!-- FAQ Item 4 -->
          <div class="bg-white rounded-xl border border-neutral-200 overflow-hidden">
            <button
              class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-neutral-50 transition-colors"
              (click)="toggleFaq(3)"
            >
              <span class="font-semibold text-neutral-900">What security measures are in place?</span>
              <svg
                class="w-5 h-5 text-neutral-500 transform transition-transform"
                [class.rotate-180]="openFaqIndex === 3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div
              class="px-6 pb-4 text-neutral-600"
              [class.hidden]="openFaqIndex !== 3"
            >
              <p>
                AI Hub is SOC 2 Type II certified with enterprise-grade security including end-to-end encryption,
                SSO integration, role-based access controls, audit logs, and compliance with GDPR, HIPAA, and other regulations.
              </p>
            </div>
          </div>

          <!-- FAQ Item 5 -->
          <div class="bg-white rounded-xl border border-neutral-200 overflow-hidden">
            <button
              class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-neutral-50 transition-colors"
              (click)="toggleFaq(4)"
            >
              <span class="font-semibold text-neutral-900">Do you offer custom pricing for large teams?</span>
              <svg
                class="w-5 h-5 text-neutral-500 transform transition-transform"
                [class.rotate-180]="openFaqIndex === 4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div
              class="px-6 pb-4 text-neutral-600"
              [class.hidden]="openFaqIndex !== 4"
            >
              <p>
                Absolutely! We offer volume discounts and custom pricing for teams of 100+ users.
                Contact our sales team to discuss your specific needs and get a personalized quote.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Final CTA -->
    <div class="text-center bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-12 text-white">
      <h3 class="text-3xl font-bold mb-4">Ready to Get Started?</h3>
      <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
        Join thousands of teams already using AI Hub to transform their productivity.
        Our sales team is standing by to help you get started.
      </p>

      <div class="flex flex-col sm:flex-row gap-4 justify-center mb-6">
        <a href="tel:******-AI-HUBS" class="btn bg-white text-primary-600 hover:bg-gray-100 btn-lg">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21L6.16 11.37a11.045 11.045 0 005.516 5.516l1.983-4.064a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
          </svg>
          Call Sales Now
        </a>
        <button class="btn bg-white/20 text-white border border-white/30 hover:bg-white/30 btn-lg">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
          Start Live Chat
        </button>
      </div>

      <div class="flex items-center justify-center space-x-6 text-sm text-white/80">
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Free consultation
        </div>
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Custom demo
        </div>
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          No commitment
        </div>
      </div>
    </div>
  </div>
</section>
