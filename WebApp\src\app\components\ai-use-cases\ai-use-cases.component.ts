import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-ai-use-cases',
  imports: [CommonModule],
  templateUrl: './ai-use-cases.component.html',
  styleUrl: './ai-use-cases.component.css'
})
export class AiUseCasesComponent {
  activeAccordion: number | null = null;

  useCases = [
    {
      id: 1,
      title: 'Boost Team Productivity',
      icon: 'productivity',
      color: 'primary',
      description: 'Empower your team with AI-driven tools that automate routine tasks and enhance creative work.',
      items: [
        {
          title: 'AI Task Generation',
          description: 'Automatically create comprehensive task lists from project descriptions, meeting notes, or simple prompts. Save hours of planning time.'
        },
        {
          title: 'Smart Workflow Creation',
          description: 'Build intelligent workflows that adapt to your team\'s patterns, automatically routing tasks and optimizing processes.'
        },
        {
          title: 'Intelligent Project Planning',
          description: 'Get AI-powered project timelines, resource allocation suggestions, and risk assessments for better planning.'
        },
        {
          title: 'Automated Progress Tracking',
          description: 'Monitor project health with AI insights, predictive analytics, and automated status updates.'
        }
      ]
    },
    {
      id: 2,
      title: 'Enhance Team Collaboration',
      icon: 'collaboration',
      color: 'secondary',
      description: 'Transform how your team communicates and shares knowledge with intelligent collaboration tools.',
      items: [
        {
          title: 'Real-time Team Coordination',
          description: 'Keep everyone aligned with AI-powered notifications, smart scheduling, and automatic conflict resolution.'
        },
        {
          title: 'Intelligent Meeting Summaries',
          description: 'Generate comprehensive meeting notes, action items, and follow-ups automatically from recordings or transcripts.'
        },
        {
          title: 'Cross-workspace Communication',
          description: 'Break down silos with AI that connects related discussions, documents, and decisions across different workspaces.'
        },
        {
          title: 'Knowledge Discovery',
          description: 'Find relevant information instantly with semantic search across all your team\'s conversations and documents.'
        }
      ]
    },
    {
      id: 3,
      title: 'Streamline Operations',
      icon: 'operations',
      color: 'accent',
      description: 'Optimize your business processes with AI-driven automation and intelligent decision support.',
      items: [
        {
          title: 'Process Automation',
          description: 'Automate repetitive workflows, approvals, and data entry tasks to reduce manual work and errors.'
        },
        {
          title: 'Data-driven Insights',
          description: 'Get actionable insights from your project data, team performance metrics, and workflow analytics.'
        },
        {
          title: 'Performance Optimization',
          description: 'Identify bottlenecks, optimize resource allocation, and improve team efficiency with AI recommendations.'
        },
        {
          title: 'Quality Assurance',
          description: 'Maintain high standards with AI-powered quality checks, compliance monitoring, and error detection.'
        }
      ]
    },
    {
      id: 4,
      title: 'Scale Your Business',
      icon: 'scale',
      color: 'warning',
      description: 'Grow confidently with enterprise-grade AI tools that scale with your organization.',
      items: [
        {
          title: 'Enterprise Security',
          description: 'Protect your data with advanced security features, compliance tools, and granular access controls.'
        },
        {
          title: 'Multi-team Management',
          description: 'Manage multiple teams and departments with centralized administration and cross-team visibility.'
        },
        {
          title: 'Custom Integrations',
          description: 'Connect with your existing tools and systems through robust APIs and custom integration solutions.'
        },
        {
          title: 'Advanced Analytics',
          description: 'Make strategic decisions with comprehensive analytics, reporting, and business intelligence features.'
        }
      ]
    }
  ];

  toggleAccordion(id: number): void {
    this.activeAccordion = this.activeAccordion === id ? null : id;
  }

  isActive(id: number): boolean {
    return this.activeAccordion === id;
  }

  getIconPath(iconType: string): string {
    const icons = {
      productivity: 'M13 10V3L4 14h7v7l9-11h-7z',
      collaboration: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
      operations: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4',
      scale: 'M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z'
    };
    return icons[iconType as keyof typeof icons] || icons.productivity;
  }

  getColorClasses(color: string): string {
    const colorMap = {
      primary: 'bg-primary-500',
      secondary: 'bg-secondary-500',
      accent: 'bg-accent-400',
      warning: 'bg-warning-400'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.primary;
  }
}
