/**
 * Responsive Design Configuration for AI Hub Marketing Website
 * Ensures optimal user experience across all devices
 */

// Breakpoint Configuration (matches Tailwind CSS)
export const BREAKPOINTS = {
  xs: '0px',      // Extra small devices
  sm: '640px',    // Small devices (landscape phones)
  md: '768px',    // Medium devices (tablets)
  lg: '1024px',   // Large devices (desktops)
  xl: '1280px',   // Extra large devices (large desktops)
  '2xl': '1536px' // 2X large devices (larger desktops)
};

// Typography Scale for Responsive Design
export const TYPOGRAPHY_SCALE = {
  // Headings - responsive sizing
  h1: {
    mobile: 'text-3xl',    // 30px
    tablet: 'text-4xl',    // 36px
    desktop: 'text-5xl'    // 48px
  },
  h2: {
    mobile: 'text-2xl',    // 24px
    tablet: 'text-3xl',    // 30px
    desktop: 'text-4xl'    // 36px
  },
  h3: {
    mobile: 'text-xl',     // 20px
    tablet: 'text-2xl',    // 24px
    desktop: 'text-3xl'    // 30px
  },
  h4: {
    mobile: 'text-lg',     // 18px
    tablet: 'text-xl',     // 20px
    desktop: 'text-2xl'    // 24px
  },
  // Body text
  body: {
    mobile: 'text-base',   // 16px
    tablet: 'text-lg',     // 18px
    desktop: 'text-lg'     // 18px
  },
  // Small text
  small: {
    mobile: 'text-sm',     // 14px
    tablet: 'text-base',   // 16px
    desktop: 'text-base'   // 16px
  }
};

// Spacing Scale for Responsive Design
export const SPACING_SCALE = {
  section: {
    mobile: 'py-12',       // 48px vertical
    tablet: 'py-16',       // 64px vertical
    desktop: 'py-20'       // 80px vertical
  },
  container: {
    mobile: 'px-4',        // 16px horizontal
    tablet: 'px-6',        // 24px horizontal
    desktop: 'px-8'        // 32px horizontal
  },
  card: {
    mobile: 'p-4',         // 16px all sides
    tablet: 'p-6',         // 24px all sides
    desktop: 'p-8'         // 32px all sides
  }
};

// Touch Target Sizes (WCAG AA compliance)
export const TOUCH_TARGETS = {
  minimum: '44px',         // WCAG minimum
  recommended: '48px',     // Recommended size
  comfortable: '56px'      // Comfortable size
};

// Grid Configuration for Different Screen Sizes
export const GRID_CONFIG = {
  features: {
    mobile: 'grid-cols-1',
    tablet: 'grid-cols-2',
    desktop: 'grid-cols-3'
  },
  testimonials: {
    mobile: 'grid-cols-1',
    tablet: 'grid-cols-2',
    desktop: 'grid-cols-3'
  },
  pricing: {
    mobile: 'grid-cols-1',
    tablet: 'grid-cols-2',
    desktop: 'grid-cols-3'
  },
  team: {
    mobile: 'grid-cols-1',
    tablet: 'grid-cols-2',
    desktop: 'grid-cols-4'
  }
};

// Image Responsive Configuration
export const IMAGE_CONFIG = {
  hero: {
    mobile: { width: 640, height: 360 },
    tablet: { width: 1024, height: 576 },
    desktop: { width: 1920, height: 1080 }
  },
  feature: {
    mobile: { width: 320, height: 240 },
    tablet: { width: 480, height: 360 },
    desktop: { width: 640, height: 480 }
  },
  avatar: {
    mobile: { width: 64, height: 64 },
    tablet: { width: 80, height: 80 },
    desktop: { width: 96, height: 96 }
  }
};

// Navigation Configuration
export const NAVIGATION_CONFIG = {
  mobile: {
    type: 'hamburger',
    position: 'fixed',
    backdrop: true
  },
  tablet: {
    type: 'horizontal',
    position: 'sticky',
    backdrop: false
  },
  desktop: {
    type: 'horizontal',
    position: 'sticky',
    backdrop: false
  }
};

// Performance Optimization Settings
export const PERFORMANCE_CONFIG = {
  lazyLoading: {
    enabled: true,
    threshold: '200px',
    placeholder: 'blur'
  },
  imageOptimization: {
    formats: ['webp', 'avif', 'jpg'],
    quality: 85,
    progressive: true
  },
  fontLoading: {
    strategy: 'swap',
    preload: ['Inter-Regular', 'Inter-Medium', 'Inter-SemiBold']
  }
};

// Accessibility Configuration
export const A11Y_CONFIG = {
  focusVisible: {
    enabled: true,
    style: 'ring-2 ring-primary-500 ring-offset-2'
  },
  skipLinks: {
    enabled: true,
    targets: ['#main-content', '#navigation', '#footer']
  },
  screenReader: {
    announcements: true,
    liveRegions: true
  },
  colorContrast: {
    level: 'AA',
    minimumRatio: 4.5
  }
};

// Utility Functions for Responsive Design
export const getResponsiveClasses = (config: any) => {
  return `${config.mobile} md:${config.tablet} lg:${config.desktop}`;
};

export const getBreakpointValue = (breakpoint: keyof typeof BREAKPOINTS) => {
  return parseInt(BREAKPOINTS[breakpoint].replace('px', ''));
};

export const isBreakpoint = (breakpoint: keyof typeof BREAKPOINTS) => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= getBreakpointValue(breakpoint);
};

// Media Query Helpers
export const MEDIA_QUERIES = {
  mobile: `(max-width: ${BREAKPOINTS.sm})`,
  tablet: `(min-width: ${BREAKPOINTS.sm}) and (max-width: ${BREAKPOINTS.lg})`,
  desktop: `(min-width: ${BREAKPOINTS.lg})`,
  touch: '(hover: none) and (pointer: coarse)',
  mouse: '(hover: hover) and (pointer: fine)',
  reducedMotion: '(prefers-reduced-motion: reduce)',
  darkMode: '(prefers-color-scheme: dark)'
};

// Component Responsive Configurations
export const COMPONENT_CONFIGS = {
  button: {
    mobile: 'px-4 py-3 text-base min-h-[48px]',
    tablet: 'px-6 py-3 text-base min-h-[48px]',
    desktop: 'px-6 py-3 text-base min-h-[48px]'
  },
  input: {
    mobile: 'px-3 py-3 text-base min-h-[48px]',
    tablet: 'px-4 py-3 text-base min-h-[48px]',
    desktop: 'px-4 py-3 text-base min-h-[48px]'
  },
  card: {
    mobile: 'p-4 rounded-lg',
    tablet: 'p-6 rounded-xl',
    desktop: 'p-8 rounded-xl'
  }
};
