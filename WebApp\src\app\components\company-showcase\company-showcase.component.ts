import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-company-showcase',
  imports: [CommonModule],
  templateUrl: './company-showcase.component.html',
  styleUrl: './company-showcase.component.css'
})
export class CompanyShowcaseComponent {
  // Company statistics
  companyStats = [
    { value: '15+', label: 'Years of Experience', icon: 'clock' },
    { value: '500+', label: 'Projects Delivered', icon: 'building' },
    { value: '200+', label: 'Happy Clients', icon: 'users' },
    { value: '25+', label: 'Countries Served', icon: 'globe' }
  ];

  // Core services
  services = [
    {
      title: 'AI & Machine Learning',
      description: 'Advanced AI solutions, chatbots, predictive analytics, and intelligent automation systems.',
      icon: 'ai',
      color: 'primary'
    },
    {
      title: 'Cloud Computing',
      description: 'AWS, Azure, Google Cloud solutions, migration services, and cloud-native application development.',
      icon: 'cloud',
      color: 'secondary'
    },
    {
      title: 'Enterprise Software',
      description: 'Custom ERP, CRM, project management, and business process automation solutions.',
      icon: 'enterprise',
      color: 'accent'
    },
    {
      title: 'Digital Transformation',
      description: 'Complete digital transformation consulting, modernization, and technology strategy services.',
      icon: 'transform',
      color: 'warning'
    }
  ];

  // Portfolio projects
  portfolio = [
    {
      title: 'AI Hub Platform',
      description: 'Our flagship AI-powered workspace management platform that revolutionizes how teams collaborate and manage projects.',
      technologies: ['AI/ML', 'Angular', 'Cloud'],
      category: 'Current Project',
      color: 'primary'
    },
    {
      title: 'Enterprise ERP Suite',
      description: 'Comprehensive enterprise resource planning solution serving 50+ multinational corporations worldwide.',
      technologies: ['.NET', 'SQL Server', 'Azure'],
      category: 'Enterprise Solution',
      color: 'secondary'
    },
    {
      title: 'Smart Analytics Platform',
      description: 'Advanced business intelligence and predictive analytics platform with real-time data processing capabilities.',
      technologies: ['Python', 'TensorFlow', 'AWS'],
      category: 'AI Analytics',
      color: 'accent'
    },
    {
      title: 'Cloud Migration Services',
      description: 'Successfully migrated 200+ enterprise applications to cloud platforms with zero downtime and enhanced performance.',
      technologies: ['AWS', 'Azure', 'GCP'],
      category: 'Cloud Services',
      color: 'warning'
    },
    {
      title: 'Mobile App Development',
      description: 'Cross-platform mobile applications with native performance, serving millions of users globally.',
      technologies: ['React Native', 'Flutter', 'Native'],
      category: 'Mobile Solutions',
      color: 'purple'
    },
    {
      title: 'Blockchain Solutions',
      description: 'Secure blockchain applications, smart contracts, and decentralized solutions for various industries.',
      technologies: ['Ethereum', 'Solidity', 'Web3'],
      category: 'Blockchain',
      color: 'indigo'
    }
  ];

  // Company highlights
  companyInfo = {
    name: 'IzonTech Solutions',
    tagline: 'Innovation Through Technology',
    mission: 'Our mission is to empower businesses with cutting-edge technology solutions that drive growth, efficiency, and competitive advantage.',
    founded: '2008',
    headquarters: 'Global',
    teamSize: '100+ Experts',
    specialization: 'AI & Cloud'
  };
}
