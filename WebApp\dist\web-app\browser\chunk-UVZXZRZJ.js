import{j as u}from"./chunk-XP6X4NQJ.js";import{a as p}from"./chunk-CUNQO24F.js";import{$a as e,Fa as m,Pa as s,Z as r,_ as o,ab as t,bb as i,jb as n,mb as c}from"./chunk-RZLO7X7E.js";var x=class d{quickStartSteps=[{title:"Installation",description:"Install AI Hub CLI and create your first project",code:`npm install -g @aihub/cli
aihub create my-project`,color:"primary"},{title:"Configuration",description:"Set up your API keys and workspace settings",code:`aihub config set api-key YOUR_KEY
aihub workspace init`,color:"secondary"},{title:"First Agent",description:"Create and deploy your first AI agent",link:"#tutorials",color:"accent"}];apiEndpoints=[{method:"POST",endpoint:"/v1/chat",description:"Create AI conversations",example:'{ "message": "Hello AI", "model": "gpt-4" }',color:"secondary"},{method:"GET",endpoint:"/v1/tasks",description:"Manage your tasks",color:"accent"},{method:"GET",endpoint:"/v1/analytics",description:"Get workspace analytics",color:"warning"}];tutorials=[{id:1,title:"Build Your First AI Agent",duration:"15 min read",description:"Learn how to create and deploy your first AI agent using our platform.",color:"primary"},{id:2,title:"Task Automation",duration:"20 min read",description:"Automate workflows with advanced task management features.",color:"secondary"},{id:3,title:"Team Collaboration",duration:"18 min read",description:"Set up collaborative workspaces for your team.",color:"accent"}];popularResources=[{title:"Installation Guide",description:"Complete setup instructions for all platforms",icon:"document",color:"primary"},{title:"Code Examples",description:"Ready-to-use code snippets and examples",icon:"code",color:"secondary"},{title:"Integrations",description:"Connect with Slack, Teams, GitHub and more",icon:"link",color:"accent"},{title:"Support",description:"Get help from our community and support team",icon:"support",color:"warning"}];scrollToSection(l){let a=document.getElementById(l);a&&a.scrollIntoView({behavior:"smooth"})}navigateToFullDocs(){window.location.href="/resources"}static \u0275fac=function(a){return new(a||d)};static \u0275cmp=s({type:d,selectors:[["app-resources"]],decls:232,vars:2,consts:[[1,"min-h-screen","bg-neutral-50"],[1,"bg-gradient-to-r","from-warning-400","to-secondary-500","text-white","py-20"],[1,"container-custom"],[1,"max-w-4xl","mx-auto","text-center"],[1,"text-4xl","md:text-5xl","font-bold","mb-6"],[1,"text-xl","text-white/90","leading-relaxed"],["id","resources",1,"py-20","bg-neutral-50"],[1,"grid","md:grid-cols-2","lg:grid-cols-4","gap-6","mb-16"],[1,"card","p-6","bg-white","border","border-neutral-200","hover-lift","text-center"],[1,"w-16","h-16","gradient-primary","rounded-2xl","flex","items-center","justify-center","mx-auto","mb-4"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-8","h-8","text-white"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 10V3L4 14h7v7l9-11h-7z"],[1,"text-xl","font-bold","text-neutral-900","mb-2"],[1,"text-neutral-600","text-sm","mb-4"],["href","#getting-started",1,"btn","btn-primary","btn-sm"],[1,"w-16","h-16","bg-secondary-500","rounded-2xl","flex","items-center","justify-center","mx-auto","mb-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"],["href","#api-docs",1,"btn","btn-secondary","btn-sm"],[1,"w-16","h-16","bg-accent-400","rounded-2xl","flex","items-center","justify-center","mx-auto","mb-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"],["href","#tutorials",1,"btn","btn-outline","btn-sm"],[1,"w-16","h-16","bg-warning-400","rounded-2xl","flex","items-center","justify-center","mx-auto","mb-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],["href","#community",1,"btn","btn-outline","btn-sm"],[1,"grid","lg:grid-cols-3","gap-8"],["id","getting-started",1,"card","p-8","bg-white","border","border-neutral-200"],[1,"flex","items-center","mb-6"],[1,"w-12","h-12","gradient-primary","rounded-xl","flex","items-center","justify-center","mr-4"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-6","h-6","text-white"],[1,"text-2xl","font-bold","text-neutral-900"],[1,"space-y-4"],[1,"border-l-4","border-primary-500","pl-4"],[1,"font-semibold","text-neutral-900","mb-2"],[1,"text-neutral-600","text-sm","mb-3"],[1,"bg-neutral-900","rounded-lg","p-3","mb-3"],[1,"text-accent-400","text-xs"],[1,"border-l-4","border-secondary-500","pl-4"],[1,"border-l-4","border-accent-400","pl-4"],[1,"text-neutral-600","text-sm"],["href","#tutorials",1,"text-primary-500","hover:text-primary-600","text-sm","font-medium"],["id","api-docs",1,"card","p-8","bg-white","border","border-neutral-200"],[1,"w-12","h-12","bg-secondary-500","rounded-xl","flex","items-center","justify-center","mr-4"],[1,"border","border-neutral-200","rounded-lg","p-4"],[1,"flex","items-center","space-x-3","mb-3"],[1,"px-2","py-1","bg-secondary-100","text-secondary-700","text-xs","font-medium","rounded"],[1,"text-sm","font-mono","text-neutral-700"],[1,"text-sm","text-neutral-600","mb-3"],[1,"bg-neutral-50","rounded","p-3"],[1,"text-xs","text-neutral-700"],[1,"px-2","py-1","bg-accent-100","text-accent-700","text-xs","font-medium","rounded"],[1,"text-sm","text-neutral-600"],[1,"px-2","py-1","bg-warning-100","text-warning-700","text-xs","font-medium","rounded"],["href","/resources",1,"btn","btn-secondary","btn-sm","w-full"],["id","tutorials",1,"card","p-8","bg-white","border","border-neutral-200"],[1,"w-12","h-12","bg-accent-400","rounded-xl","flex","items-center","justify-center","mr-4"],[1,"border","border-neutral-200","rounded-lg","p-4","hover-lift"],[1,"w-8","h-8","bg-primary-100","rounded-lg","flex","items-center","justify-center"],[1,"text-primary-600","font-bold","text-sm"],[1,"font-semibold","text-neutral-900"],[1,"text-xs","text-neutral-500"],["href","/resources",1,"text-primary-500","hover:text-primary-600","text-sm","font-medium"],[1,"w-8","h-8","bg-secondary-100","rounded-lg","flex","items-center","justify-center"],[1,"text-secondary-600","font-bold","text-sm"],[1,"w-8","h-8","bg-accent-100","rounded-lg","flex","items-center","justify-center"],[1,"text-accent-600","font-bold","text-sm"],["href","/resources",1,"btn","btn-outline","btn-sm","w-full"],[1,"mt-16"],[1,"text-2xl","font-bold","text-neutral-900","mb-8","text-center"],[1,"grid","md:grid-cols-2","lg:grid-cols-4","gap-6"],[1,"card","p-6","bg-white","border","border-neutral-200","hover-lift"],[1,"w-10","h-10","bg-primary-100","rounded-lg","flex","items-center","justify-center","mb-4"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-primary-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"w-10","h-10","bg-secondary-100","rounded-lg","flex","items-center","justify-center","mb-4"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-secondary-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"],[1,"w-10","h-10","bg-accent-100","rounded-lg","flex","items-center","justify-center","mb-4"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-accent-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"],[1,"w-10","h-10","bg-warning-100","rounded-lg","flex","items-center","justify-center","mb-4"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"w-5","h-5","text-warning-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"],[1,"mt-16","text-center"],[1,"card","p-8","bg-gradient-to-br","from-primary-50","to-secondary-50","border","border-primary-200"],[1,"text-2xl","font-bold","text-neutral-900","mb-4"],[1,"text-neutral-600","mb-6","max-w-2xl","mx-auto"],[1,"flex","flex-col","sm:flex-row","gap-4","justify-center"],["href","/resources",1,"btn","btn-primary"],["href","#community",1,"btn","btn-outline"]],template:function(a,b){a&1&&(e(0,"div",0)(1,"section",1)(2,"div",2)(3,"div",3)(4,"h1",4),n(5,"Resources & Documentation"),t(),e(6,"p",5),n(7," Everything you need to get started with AI Hub. From quick guides to comprehensive documentation. "),t()()()(),e(8,"section",6)(9,"div",2)(10,"div",7)(11,"div",8)(12,"div",9),r(),e(13,"svg",10),i(14,"path",11),t()(),o(),e(15,"h3",12),n(16,"Quick Start"),t(),e(17,"p",13),n(18,"Get up and running in minutes with our step-by-step guide"),t(),e(19,"a",14),n(20,"Start Now"),t()(),e(21,"div",8)(22,"div",15),r(),e(23,"svg",10),i(24,"path",16),t()(),o(),e(25,"h3",12),n(26,"API Reference"),t(),e(27,"p",13),n(28,"Complete API documentation with examples and guides"),t(),e(29,"a",17),n(30,"View API"),t()(),e(31,"div",8)(32,"div",18),r(),e(33,"svg",10),i(34,"path",19),t()(),o(),e(35,"h3",12),n(36,"Tutorials"),t(),e(37,"p",13),n(38,"Step-by-step tutorials for common use cases"),t(),e(39,"a",20),n(40,"Learn More"),t()(),e(41,"div",8)(42,"div",21),r(),e(43,"svg",10),i(44,"path",22),t()(),o(),e(45,"h3",12),n(46,"Community"),t(),e(47,"p",13),n(48,"Join our community for support and discussions"),t(),e(49,"a",23),n(50,"Join Now"),t()()(),e(51,"div",24)(52,"div",25)(53,"div",26)(54,"div",27),r(),e(55,"svg",28),i(56,"path",11),t()(),o(),e(57,"h3",29),n(58,"Getting Started"),t()(),e(59,"div",30)(60,"div",31)(61,"h4",32),n(62,"1. Installation"),t(),e(63,"p",33),n(64,"Install AI Hub CLI and create your first project"),t(),e(65,"div",34)(66,"code",35),n(67," npm install -g @aihub/cli"),i(68,"br"),n(69," aihub create my-project "),t()()(),e(70,"div",36)(71,"h4",32),n(72,"2. Configuration"),t(),e(73,"p",33),n(74,"Set up your API keys and workspace settings"),t(),e(75,"div",34)(76,"code",35),n(77," aihub config set api-key YOUR_KEY"),i(78,"br"),n(79," aihub workspace init "),t()()(),e(80,"div",37)(81,"h4",32),n(82,"3. First Agent"),t(),e(83,"p",38),n(84,"Create and deploy your first AI agent"),t(),e(85,"a",39),n(86," View Tutorial \u2192 "),t()()()(),e(87,"div",40)(88,"div",26)(89,"div",41),r(),e(90,"svg",28),i(91,"path",16),t()(),o(),e(92,"h3",29),n(93,"API Reference"),t()(),e(94,"div",30)(95,"div",42)(96,"div",43)(97,"span",44),n(98,"POST"),t(),e(99,"code",45),n(100,"/v1/chat"),t()(),e(101,"p",46),n(102,"Create AI conversations"),t(),e(103,"div",47)(104,"code",48),n(105),t()()(),e(106,"div",42)(107,"div",43)(108,"span",49),n(109,"GET"),t(),e(110,"code",45),n(111,"/v1/tasks"),t()(),e(112,"p",50),n(113,"Manage your tasks"),t()(),e(114,"div",42)(115,"div",43)(116,"span",51),n(117,"GET"),t(),e(118,"code",45),n(119,"/v1/analytics"),t()(),e(120,"p",50),n(121,"Get workspace analytics"),t()(),e(122,"a",52),n(123," View Full API Docs "),t()()(),e(124,"div",53)(125,"div",26)(126,"div",54),r(),e(127,"svg",28),i(128,"path",19),t()(),o(),e(129,"h3",29),n(130,"Tutorials"),t()(),e(131,"div",30)(132,"div",55)(133,"div",43)(134,"div",56)(135,"span",57),n(136,"1"),t()(),e(137,"div")(138,"h4",58),n(139,"Build Your First AI Agent"),t(),e(140,"p",59),n(141,"15 min read"),t()()(),e(142,"p",33),n(143," Learn how to create and deploy your first AI agent using our platform. "),t(),e(144,"a",60),n(145," Start Tutorial \u2192 "),t()(),e(146,"div",55)(147,"div",43)(148,"div",61)(149,"span",62),n(150,"2"),t()(),e(151,"div")(152,"h4",58),n(153,"Task Automation"),t(),e(154,"p",59),n(155,"20 min read"),t()()(),e(156,"p",33),n(157," Automate workflows with advanced task management features. "),t(),e(158,"a",60),n(159," Start Tutorial \u2192 "),t()(),e(160,"div",55)(161,"div",43)(162,"div",63)(163,"span",64),n(164,"3"),t()(),e(165,"div")(166,"h4",58),n(167,"Team Collaboration"),t(),e(168,"p",59),n(169,"18 min read"),t()()(),e(170,"p",33),n(171," Set up collaborative workspaces for your team. "),t(),e(172,"a",60),n(173," Start Tutorial \u2192 "),t()(),e(174,"a",65),n(175," View All Tutorials "),t()()()(),e(176,"div",66)(177,"h3",67),n(178,"Popular Resources"),t(),e(179,"div",68)(180,"div",69)(181,"div",70),r(),e(182,"svg",71),i(183,"path",72),t()(),o(),e(184,"h4",32),n(185,"Installation Guide"),t(),e(186,"p",13),n(187,"Complete setup instructions for all platforms"),t(),e(188,"a",60),n(189," Read Guide \u2192 "),t()(),e(190,"div",69)(191,"div",73),r(),e(192,"svg",74),i(193,"path",75),t()(),o(),e(194,"h4",32),n(195,"Code Examples"),t(),e(196,"p",13),n(197,"Ready-to-use code snippets and examples"),t(),e(198,"a",60),n(199," View Examples \u2192 "),t()(),e(200,"div",69)(201,"div",76),r(),e(202,"svg",77),i(203,"path",78),t()(),o(),e(204,"h4",32),n(205,"Integrations"),t(),e(206,"p",13),n(207,"Connect with Slack, Teams, GitHub and more"),t(),e(208,"a",60),n(209," View Integrations \u2192 "),t()(),e(210,"div",69)(211,"div",79),r(),e(212,"svg",80),i(213,"path",81),t()(),o(),e(214,"h4",32),n(215,"Support"),t(),e(216,"p",13),n(217,"Get help from our community and support team"),t(),e(218,"a",60),n(219," Get Support \u2192 "),t()()()(),e(220,"div",82)(221,"div",83)(222,"h3",84),n(223,"Need More Help?"),t(),e(224,"p",85),n(225," Can't find what you're looking for? Our comprehensive documentation has everything you need to succeed with AI Hub. "),t(),e(226,"div",86)(227,"a",87),n(228," View Full Documentation "),t(),e(229,"a",88),n(230," Join Community "),t()()()()()(),i(231,"app-cta-section"),t()),a&2&&(m(105),c(" ","{",' "message": "Hello AI", "model": "gpt-4" ',"}"," "))},dependencies:[u,p],styles:["html[_ngcontent-%COMP%]{scroll-behavior:smooth}.code-block[_ngcontent-%COMP%]{background-color:#1a1a1a;border-radius:.5rem;padding:.75rem;font-family:Monaco,Menlo,Ubuntu Mono,monospace;font-size:.75rem;line-height:1.4;overflow-x:auto}.code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{color:#10b981}.hover-lift[_ngcontent-%COMP%]{transition:all .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 10px 25px #0000001a}.method-badge[_ngcontent-%COMP%]{font-size:.75rem;font-weight:600;padding:.25rem .5rem;border-radius:.375rem;text-transform:uppercase}.tutorial-card[_ngcontent-%COMP%]{transition:all .3s ease;border:1px solid #e5e5e5}.tutorial-card[_ngcontent-%COMP%]:hover{border-color:#3b82f6;box-shadow:0 4px 12px #0000001a}.resource-icon[_ngcontent-%COMP%]{transition:all .3s ease}.resource-icon[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.gradient-bg[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2)}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}@media (max-width: 768px){.code-block[_ngcontent-%COMP%]{font-size:.625rem;padding:.5rem}.tutorial-card[_ngcontent-%COMP%]{margin-bottom:1rem}}.border-primary-accent[_ngcontent-%COMP%]{border-left-color:#3b82f6}.border-secondary-accent[_ngcontent-%COMP%]{border-left-color:#8b5cf6}.border-accent-accent[_ngcontent-%COMP%]{border-left-color:#10b981}.btn-hover-scale[_ngcontent-%COMP%]{transition:transform .2s ease}.btn-hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.loading[_ngcontent-%COMP%]{opacity:.6;pointer-events:none}.code-block[_ngcontent-%COMP%]::-webkit-scrollbar{height:4px}.code-block[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#2d2d2d}.code-block[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#10b981;border-radius:2px}.code-block[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#059669}"]})};export{x as ResourcesComponent};
