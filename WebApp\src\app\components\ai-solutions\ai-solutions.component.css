/* AI Solutions Component Styles */

/* Hover lift effect for solution cards */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Icon container animations */
.group:hover .w-16 {
  transform: scale(1.1);
}

/* Feature list styling */
.space-y-2 > * + * {
  margin-top: 0.5rem;
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Custom color utilities for dynamic classes */
.bg-success-500 {
  background-color: rgb(34, 197, 94);
}

.bg-info-500 {
  background-color: rgb(59, 130, 246);
}

.group-hover\:bg-success-600:hover {
  background-color: rgb(22, 163, 74);
}

.group-hover\:bg-info-600:hover {
  background-color: rgb(37, 99, 235);
}

/* Arrow animation on hover */
.group:hover .translate-x-0 {
  transform: translateX(0.5rem);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hover-lift:hover {
    transform: translateY(-4px);
  }
}
