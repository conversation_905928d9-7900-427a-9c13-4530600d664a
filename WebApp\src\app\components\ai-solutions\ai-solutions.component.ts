import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-ai-solutions',
  imports: [CommonModule],
  templateUrl: './ai-solutions.component.html',
  styleUrl: './ai-solutions.component.css'
})
export class AiSolutionsComponent {
  solutions = [
    {
      id: 1,
      title: 'AI Agents & Assistants',
      description: 'Create intelligent AI agents that understand context, learn from interactions, and provide personalized assistance for your team\'s specific workflows and tasks.',
      icon: 'robot',
      color: 'primary',
      features: ['Natural Language Processing', 'Context-Aware Responses', 'Custom Training', '24/7 Availability']
    },
    {
      id: 2,
      title: 'Workflow Automation',
      description: 'Streamline your operations with AI-driven automation that optimizes processes, reduces manual work, and enhances decision-making across your organization.',
      icon: 'automation',
      color: 'secondary',
      features: ['Process Optimization', 'Smart Routing', 'Auto-Scheduling', 'Error Reduction']
    },
    {
      id: 3,
      title: 'Smart Project Management',
      description: 'Transform project planning and execution with AI-powered insights, predictive analytics, and intelligent resource allocation for maximum efficiency.',
      icon: 'project',
      color: 'accent',
      features: ['Predictive Planning', 'Resource Optimization', 'Risk Assessment', 'Progress Tracking']
    },
    {
      id: 4,
      title: 'Team Collaboration Hub',
      description: 'Enhance team productivity with AI-facilitated communication, smart meeting summaries, and intelligent knowledge sharing across workspaces.',
      icon: 'team',
      color: 'warning',
      features: ['Smart Summaries', 'Knowledge Sharing', 'Real-time Sync', 'Cross-team Communication']
    },
    {
      id: 5,
      title: 'Custom AI Development',
      description: 'Build tailored AI solutions that fit your unique business needs with our flexible platform and comprehensive development tools.',
      icon: 'code',
      color: 'success',
      features: ['Custom Models', 'API Integration', 'Scalable Architecture', 'Enterprise Security']
    },
    {
      id: 6,
      title: 'Enterprise Integration',
      description: 'Seamlessly connect AI Hub with your existing tools and systems through robust APIs, webhooks, and pre-built integrations.',
      icon: 'integration',
      color: 'info',
      features: ['API Connectivity', 'Data Synchronization', 'Security Compliance', 'Multi-platform Support']
    }
  ];

  getIconPath(iconType: string): string {
    const icons = {
      robot: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z',
      automation: 'M13 10V3L4 14h7v7l9-11h-7z',
      project: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01',
      team: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
      code: 'M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4',
      integration: 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1'
    };
    return icons[iconType as keyof typeof icons] || icons.robot;
  }

  getColorClasses(color: string): string {
    const colorMap = {
      primary: 'bg-primary-500 group-hover:bg-primary-600',
      secondary: 'bg-secondary-500 group-hover:bg-secondary-600',
      accent: 'bg-accent-400 group-hover:bg-accent-500',
      warning: 'bg-warning-400 group-hover:bg-warning-500',
      success: 'bg-success-500 group-hover:bg-success-600',
      info: 'bg-info-500 group-hover:bg-info-600'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.primary;
  }

  onSolutionClick(solution: any) {
    console.log('Solution clicked:', solution.title);
    // Future: Navigate to detailed solution page
  }
}
