<!-- Resources Page -->
<div class="min-h-screen bg-neutral-50">
  <!-- <PERSON> Header -->
  <section class="bg-gradient-to-r from-warning-400 to-secondary-500 text-white py-20">
    <div class="container-custom">
      <div class="max-w-4xl mx-auto text-center">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">Resources & Documentation</h1>
        <p class="text-xl text-white/90 leading-relaxed">
          Everything you need to get started with AI Hub. From quick guides to comprehensive documentation.
        </p>
      </div>
    </div>
  </section>

  <!-- Resources Content -->
  <section id="resources" class="py-20 bg-neutral-50">
    <div class="container-custom">
      <!-- Quick Access Cards -->
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
        <!-- Getting Started -->
        <div class="card p-6 bg-white border border-neutral-200 hover-lift text-center">
          <div class="w-16 h-16 gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z">
              </path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-neutral-900 mb-2">Quick Start</h3>
          <p class="text-neutral-600 text-sm mb-4">Get up and running in minutes with our step-by-step guide</p>
          <a href="#getting-started" class="btn btn-primary btn-sm">Start Now</a>
        </div>

        <!-- API Documentation -->
        <div class="card p-6 bg-white border border-neutral-200 hover-lift text-center">
          <div class="w-16 h-16 bg-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-neutral-900 mb-2">API Reference</h3>
          <p class="text-neutral-600 text-sm mb-4">Complete API documentation with examples and guides</p>
          <a href="#api-docs" class="btn btn-secondary btn-sm">View API</a>
        </div>

        <!-- Tutorials -->
        <div class="card p-6 bg-white border border-neutral-200 hover-lift text-center">
          <div class="w-16 h-16 bg-accent-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
              </path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-neutral-900 mb-2">Tutorials</h3>
          <p class="text-neutral-600 text-sm mb-4">Step-by-step tutorials for common use cases</p>
          <a href="#tutorials" class="btn btn-outline btn-sm">Learn More</a>
        </div>

        <!-- Community -->
        <div class="card p-6 bg-white border border-neutral-200 hover-lift text-center">
          <div class="w-16 h-16 bg-warning-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
              </path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-neutral-900 mb-2">Community</h3>
          <p class="text-neutral-600 text-sm mb-4">Join our community for support and discussions</p>
          <a href="#community" class="btn btn-outline btn-sm">Join Now</a>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid lg:grid-cols-3 gap-8">
        <!-- Getting Started -->
        <div id="getting-started" class="card p-8 bg-white border border-neutral-200">
          <div class="flex items-center mb-6">
            <div class="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z">
                </path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-neutral-900">Getting Started</h3>
          </div>

          <div class="space-y-4">
            <div class="border-l-4 border-primary-500 pl-4">
              <h4 class="font-semibold text-neutral-900 mb-2">1. Installation</h4>
              <p class="text-neutral-600 text-sm mb-3">Install AI Hub CLI and create your first project</p>
              <div class="bg-neutral-900 rounded-lg p-3 mb-3">
                <code class="text-accent-400 text-xs">
                npm install -g &#64;aihub/cli<br>
                aihub create my-project
              </code>
              </div>
            </div>

            <div class="border-l-4 border-secondary-500 pl-4">
              <h4 class="font-semibold text-neutral-900 mb-2">2. Configuration</h4>
              <p class="text-neutral-600 text-sm mb-3">Set up your API keys and workspace settings</p>
              <div class="bg-neutral-900 rounded-lg p-3 mb-3">
                <code class="text-accent-400 text-xs">
                aihub config set api-key YOUR_KEY<br>
                aihub workspace init
              </code>
              </div>
            </div>

            <div class="border-l-4 border-accent-400 pl-4">
              <h4 class="font-semibold text-neutral-900 mb-2">3. First Agent</h4>
              <p class="text-neutral-600 text-sm">Create and deploy your first AI agent</p>
              <a href="#tutorials" class="text-primary-500 hover:text-primary-600 text-sm font-medium">
                View Tutorial →
              </a>
            </div>
          </div>
        </div>

        <!-- API Reference -->
        <div id="api-docs" class="card p-8 bg-white border border-neutral-200">
          <div class="flex items-center mb-6">
            <div class="w-12 h-12 bg-secondary-500 rounded-xl flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-neutral-900">API Reference</h3>
          </div>

          <div class="space-y-4">
            <!-- Chat API -->
            <div class="border border-neutral-200 rounded-lg p-4">
              <div class="flex items-center space-x-3 mb-3">
                <span class="px-2 py-1 bg-secondary-100 text-secondary-700 text-xs font-medium rounded">POST</span>
                <code class="text-sm font-mono text-neutral-700">/v1/chat</code>
              </div>
              <p class="text-sm text-neutral-600 mb-3">Create AI conversations</p>
              <div class="bg-neutral-50 rounded p-3">
                <code class="text-xs text-neutral-700">
                {{'{'}} "message": "Hello AI", "model": "gpt-4" {{'}'}}
              </code>
              </div>
            </div>

            <!-- Tasks API -->
            <div class="border border-neutral-200 rounded-lg p-4">
              <div class="flex items-center space-x-3 mb-3">
                <span class="px-2 py-1 bg-accent-100 text-accent-700 text-xs font-medium rounded">GET</span>
                <code class="text-sm font-mono text-neutral-700">/v1/tasks</code>
              </div>
              <p class="text-sm text-neutral-600">Manage your tasks</p>
            </div>

            <!-- Analytics API -->
            <div class="border border-neutral-200 rounded-lg p-4">
              <div class="flex items-center space-x-3 mb-3">
                <span class="px-2 py-1 bg-warning-100 text-warning-700 text-xs font-medium rounded">GET</span>
                <code class="text-sm font-mono text-neutral-700">/v1/analytics</code>
              </div>
              <p class="text-sm text-neutral-600">Get workspace analytics</p>
            </div>

            <a href="/resources" class="btn btn-secondary btn-sm w-full">
              View Full API Docs
            </a>
          </div>
        </div>

        <!-- Tutorials & Guides -->
        <div id="tutorials" class="card p-8 bg-white border border-neutral-200">
          <div class="flex items-center mb-6">
            <div class="w-12 h-12 bg-accent-400 rounded-xl flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                </path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-neutral-900">Tutorials</h3>
          </div>

          <div class="space-y-4">
            <!-- Tutorial 1 -->
            <div class="border border-neutral-200 rounded-lg p-4 hover-lift">
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                  <span class="text-primary-600 font-bold text-sm">1</span>
                </div>
                <div>
                  <h4 class="font-semibold text-neutral-900">Build Your First AI Agent</h4>
                  <p class="text-xs text-neutral-500">15 min read</p>
                </div>
              </div>
              <p class="text-neutral-600 text-sm mb-3">
                Learn how to create and deploy your first AI agent using our platform.
              </p>
              <a href="/resources" class="text-primary-500 hover:text-primary-600 text-sm font-medium">
                Start Tutorial →
              </a>
            </div>

            <!-- Tutorial 2 -->
            <div class="border border-neutral-200 rounded-lg p-4 hover-lift">
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center">
                  <span class="text-secondary-600 font-bold text-sm">2</span>
                </div>
                <div>
                  <h4 class="font-semibold text-neutral-900">Task Automation</h4>
                  <p class="text-xs text-neutral-500">20 min read</p>
                </div>
              </div>
              <p class="text-neutral-600 text-sm mb-3">
                Automate workflows with advanced task management features.
              </p>
              <a href="/resources" class="text-primary-500 hover:text-primary-600 text-sm font-medium">
                Start Tutorial →
              </a>
            </div>

            <!-- Tutorial 3 -->
            <div class="border border-neutral-200 rounded-lg p-4 hover-lift">
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center">
                  <span class="text-accent-600 font-bold text-sm">3</span>
                </div>
                <div>
                  <h4 class="font-semibold text-neutral-900">Team Collaboration</h4>
                  <p class="text-xs text-neutral-500">18 min read</p>
                </div>
              </div>
              <p class="text-neutral-600 text-sm mb-3">
                Set up collaborative workspaces for your team.
              </p>
              <a href="/resources" class="text-primary-500 hover:text-primary-600 text-sm font-medium">
                Start Tutorial →
              </a>
            </div>

            <a href="/resources" class="btn btn-outline btn-sm w-full">
              View All Tutorials
            </a>
          </div>
        </div>
      </div>

      <!-- Popular Resources -->
      <div class="mt-16">
        <h3 class="text-2xl font-bold text-neutral-900 mb-8 text-center">Popular Resources</h3>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Resource 1 -->
          <div class="card p-6 bg-white border border-neutral-200 hover-lift">
            <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                </path>
              </svg>
            </div>
            <h4 class="font-semibold text-neutral-900 mb-2">Installation Guide</h4>
            <p class="text-neutral-600 text-sm mb-4">Complete setup instructions for all platforms</p>
            <a href="/resources" class="text-primary-500 hover:text-primary-600 text-sm font-medium">
              Read Guide →
            </a>
          </div>

          <!-- Resource 2 -->
          <div class="card p-6 bg-white border border-neutral-200 hover-lift">
            <div class="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-5 h-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h4 class="font-semibold text-neutral-900 mb-2">Code Examples</h4>
            <p class="text-neutral-600 text-sm mb-4">Ready-to-use code snippets and examples</p>
            <a href="/resources" class="text-primary-500 hover:text-primary-600 text-sm font-medium">
              View Examples →
            </a>
          </div>

          <!-- Resource 3 -->
          <div class="card p-6 bg-white border border-neutral-200 hover-lift">
            <div class="w-10 h-10 bg-accent-100 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1">
                </path>
              </svg>
            </div>
            <h4 class="font-semibold text-neutral-900 mb-2">Integrations</h4>
            <p class="text-neutral-600 text-sm mb-4">Connect with Slack, Teams, GitHub and more</p>
            <a href="/resources" class="text-primary-500 hover:text-primary-600 text-sm font-medium">
              View Integrations →
            </a>
          </div>

          <!-- Resource 4 -->
          <div class="card p-6 bg-white border border-neutral-200 hover-lift">
            <div class="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-5 h-5 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z">
                </path>
              </svg>
            </div>
            <h4 class="font-semibold text-neutral-900 mb-2">Support</h4>
            <p class="text-neutral-600 text-sm mb-4">Get help from our community and support team</p>
            <a href="/resources" class="text-primary-500 hover:text-primary-600 text-sm font-medium">
              Get Support →
            </a>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="mt-16 text-center">
        <div class="card p-8 bg-gradient-to-br from-primary-50 to-secondary-50 border border-primary-200">
          <h3 class="text-2xl font-bold text-neutral-900 mb-4">Need More Help?</h3>
          <p class="text-neutral-600 mb-6 max-w-2xl mx-auto">
            Can't find what you're looking for? Our comprehensive documentation has everything you need to succeed with
            AI Hub.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/resources" class="btn btn-primary">
              View Full Documentation
            </a>
            <a href="#community" class="btn btn-outline">
              Join Community
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Call to Action -->
  <app-cta-section></app-cta-section>
</div>
