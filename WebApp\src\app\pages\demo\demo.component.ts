import { Component, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { DashboardPreviewComponent } from '../../components/dashboard-preview/dashboard-preview.component';
import { CtaSectionComponent } from '../../components/cta-section/cta-section.component';

@Component({
  selector: 'app-demo',
  imports: [
    CommonModule,
    DashboardPreviewComponent,
    CtaSectionComponent
  ],
  templateUrl: './demo.component.html',
  styleUrl: './demo.component.css'
})
export class DemoComponent {
  constructor(private sanitizer: DomSanitizer) {}

  // Demo features data
  demoFeatures = [
    {
      title: 'AI Chat Assistant',
      description: 'Experience intelligent conversations with our advanced AI chat system',
      icon: 'chat',
      demoUrl: '#ai-chat-demo',
      color: 'secondary'
    },
    {
      title: 'Task Automation',
      description: 'See how AI Hub automates complex workflows and task management',
      icon: 'automation',
      demoUrl: '#task-demo',
      color: 'accent'
    },
    {
      title: 'Analytics Dashboard',
      description: 'Explore real-time analytics and performance insights',
      icon: 'analytics',
      demoUrl: '#analytics-demo',
      color: 'primary'
    },
    {
      title: 'Team Collaboration',
      description: 'Discover collaborative workspaces and team management tools',
      icon: 'team',
      demoUrl: '#collaboration-demo',
      color: 'warning'
    }
  ];

  // Video demos data
  videoDemos = [
    {
      title: 'Getting Started with AI Hub',
      duration: '3:45',
      description: 'A quick overview of AI Hub features and setup process',
      thumbnail: '/assets/demo-thumbnails/getting-started.jpg',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&rel=0',
      youtubeId: 'dQw4w9WgXcQ'
    },
    {
      title: 'Advanced AI Workflows',
      duration: '7:20',
      description: 'Deep dive into creating complex AI-powered workflows',
      thumbnail: '/assets/demo-thumbnails/workflows.jpg',
      videoUrl: 'https://www.youtube.com/embed/ScMzIvxBSi4?autoplay=1&rel=0',
      youtubeId: 'ScMzIvxBSi4'
    },
    {
      title: 'Team Collaboration Features',
      duration: '5:15',
      description: 'How teams can collaborate effectively using AI Hub',
      thumbnail: '/assets/demo-thumbnails/collaboration.jpg',
      videoUrl: 'https://www.youtube.com/embed/jNQXAC9IVRw?autoplay=1&rel=0',
      youtubeId: 'jNQXAC9IVRw'
    },
    {
      title: 'AI Chat Assistant Demo',
      duration: '4:30',
      description: 'See our AI chat assistant in action with real conversations',
      thumbnail: '/assets/demo-thumbnails/ai-chat.jpg',
      videoUrl: 'https://www.youtube.com/embed/M7lc1UVf-VE?autoplay=1&rel=0',
      youtubeId: 'M7lc1UVf-VE'
    },
    {
      title: 'Analytics Dashboard Walkthrough',
      duration: '6:10',
      description: 'Explore the powerful analytics and reporting features',
      thumbnail: '/assets/demo-thumbnails/analytics.jpg',
      videoUrl: 'https://www.youtube.com/embed/kJQP7kiw5Fk?autoplay=1&rel=0',
      youtubeId: 'kJQP7kiw5Fk'
    },
    {
      title: 'Mobile App Demo',
      duration: '2:55',
      description: 'AI Hub on the go - mobile app features and capabilities',
      thumbnail: '/assets/demo-thumbnails/mobile.jpg',
      videoUrl: 'https://www.youtube.com/embed/LDU_Txk06tM?autoplay=1&rel=0',
      youtubeId: 'LDU_Txk06tM'
    }
  ];

  // Current playing video
  currentVideo: SafeResourceUrl | null = null;

  // Methods
  playDemo(demoUrl: string) {
    // Handle demo interaction
    console.log('Playing demo:', demoUrl);
  }

  playVideo(video: any) {
    // Set current video to show in modal or embedded player
    this.currentVideo = this.sanitizer.bypassSecurityTrustResourceUrl(video.videoUrl);
    console.log('Playing video:', video.title);
  }

  closeVideo() {
    this.currentVideo = null;
  }

  scheduleDemo() {
    // Handle demo scheduling
    console.log('Scheduling live demo');
    // You can integrate with calendly or similar service
    window.open('https://calendly.com/your-demo-link', '_blank');
  }

  requestTrial() {
    // Handle trial request
    console.log('Requesting free trial');
    // Redirect to trial signup page
    window.open('/signup?trial=true', '_blank');
  }

  // Keyboard event handler for ESC key
  @HostListener('document:keydown.escape')
  onEscapeKey() {
    if (this.currentVideo) {
      this.closeVideo();
    }
  }
}
