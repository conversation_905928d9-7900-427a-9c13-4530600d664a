/* Footer specific styles to ensure text visibility */
footer {
  background-color: #171717 !important; /* neutral-900 */
  color: #ffffff !important;
}

/* Brand section */
footer h3 {
  color: #ffffff !important;
  font-weight: 700;
}

footer .text-neutral-400 {
  color: #ffffff !important;
}

/* Section headers */
footer h4 {
  color: #ffffff !important;
  font-weight: 600;
}

/* All paragraphs and text */
footer p {
  color: #ffffff !important;
}

/* All links */
footer a {
  color: #ffffff !important;
  text-decoration: none;
  transition: color 0.2s ease;
}

footer a:hover {
  color: #e5e5e5 !important;
}

/* List items */
footer li {
  color: #ffffff !important;
}

/* Bottom section */
footer .border-t {
  border-color: #404040 !important;
}

/* Ensure all text elements are white */
footer * {
  color: #ffffff !important;
}

/* Override any Tailwind classes that might make text dark */
footer .text-sm,
footer .text-xs,
footer .text-lg {
  color: #ffffff !important;
}

/* Hover states for better UX */
footer a:hover {
  color: #d1d5db !important;
  opacity: 0.8;
}
