/* You can add global styles to this file, and also import other style files */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* AI Hub Primary Colors - Deep Indigo */
  --color-primary: #2A2E5B;        /* Deep Indigo - Main brand */
  --color-primary-dark: #242847;   /* Darker for hover states */
  --color-primary-light: #8B94FF;  /* Light accent */

  /* Electric Blue - AI-inspired accent */
  --color-secondary: #3C9EE7;      /* Electric Blue - Main accent */
  --color-secondary-dark: #0284C7; /* Darker blue */
  --color-accent: #34D399;         /* Emerald Green - Positive actions */
  --color-success: #34D399;        /* Emerald Green - Success states */
  --color-warning: #FBBF24;        /* Amber/Gold - Highlights */
  --color-error: #DC2626;          /* Keep error red */

  /* AI Hub Neutral Colors */
  --color-neutral-50: #F9FAFB;     /* Cloud White - Clean background */
  --color-neutral-100: #F3F4F6;    /* Card backgrounds */
  --color-neutral-200: #E5E7EB;    /* Light borders */
  --color-neutral-300: #D1D5DB;    /* Dividers, borders */
  --color-neutral-400: #9CA3AF;    /* Disabled states */
  --color-neutral-500: #6B7280;    /* Subtext, muted UI elements */
  --color-neutral-600: #4B5563;    /* Secondary text */
  --color-neutral-700: #374151;    /* Body text */
  --color-neutral-800: #1F2937;    /* Dark text */
  --color-neutral-900: #111827;    /* Charcoal Gray - Rich text */

  /* Text Colors - AI Hub Optimized */
  --color-text-primary: #111827;   /* Charcoal Gray - Rich text */
  --color-text-secondary: #374151; /* Body text */
  --color-text-muted: #6B7280;     /* Subtext, muted UI elements */
  --color-text-inverse: #FFFFFF;   /* White text on dark backgrounds */

  /* Background Colors - AI Hub Clean */
  --color-bg-primary: #FFFFFF;     /* Pure white */
  --color-bg-secondary: #F9FAFB;   /* Cloud White - Clean background */
  --color-bg-tertiary: #F3F4F6;    /* Card backgrounds */

  /* Border Colors - AI Hub Professional */
  --color-border-light: #E5E7EB;   /* Light borders */
  --color-border-medium: #D1D5DB;  /* Dividers, borders */
  --color-border-dark: #9CA3AF;    /* Disabled states */

  /* AI Hub Gradients - Tech-inspired */
  --gradient-primary: linear-gradient(135deg, #2A2E5B 0%, #3C9EE7 100%);
  --gradient-secondary: linear-gradient(135deg, #3C9EE7 0%, #34D399 100%);
  --gradient-hero: linear-gradient(135deg, #2A2E5B 0%, #3C9EE7 50%, #34D399 100%);
  --gradient-card: linear-gradient(145deg, #FFFFFF 0%, #F9FAFB 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-primary: 0 10px 25px -5px rgba(42, 46, 91, 0.3);    /* Deep Indigo shadow */
  --shadow-secondary: 0 10px 25px -5px rgba(60, 158, 231, 0.3); /* Electric Blue shadow */

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===== DARK THEME VARIABLES ===== */
[data-theme="dark"] {
  /* AI Hub Dark Theme - Primary Colors */
  --color-primary: #8B94FF;        /* Lighter indigo for dark theme */
  --color-primary-dark: #7C85FF;   /* Slightly darker */
  --color-secondary: #3C9EE7;      /* Electric Blue stays vibrant */
  --color-accent: #34D399;         /* Emerald Green for contrast */

  /* Text Colors */
  --color-text-primary: #F1F5F9;
  --color-text-secondary: #CBD5E1;
  --color-text-muted: #94A3B8;
  --color-text-inverse: #0F172A;

  /* Background Colors */
  --color-bg-primary: #0A0A0B;
  --color-bg-secondary: #1A1A1B;
  --color-bg-tertiary: #2A2A2B;

  /* Border Colors */
  --color-border-light: #2F2F30;
  --color-border-medium: #404040;
  --color-border-dark: #525252;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
  --gradient-card: linear-gradient(145deg, #1A1A1B 0%, #2A2A2B 100%);

  /* Shadows (adjusted for dark theme) */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 10px 25px -5px rgba(139, 148, 255, 0.4);  /* Light indigo shadow */
  --shadow-secondary: 0 10px 25px -5px rgba(60, 158, 231, 0.4); /* Electric blue shadow */
}

/* ===== BASE STYLES ===== */
@layer base {
  html {
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    line-height: var(--line-height-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--transition-normal), color var(--transition-normal);
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: var(--color-text-primary);
    margin: 0;
  }

  h1 { font-size: var(--font-size-4xl); }
  h2 { font-size: var(--font-size-3xl); }
  h3 { font-size: var(--font-size-2xl); }
  h4 { font-size: var(--font-size-xl); }
  h5 { font-size: var(--font-size-lg); }
  h6 { font-size: var(--font-size-base); }

  p {
    margin: 0;
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
  }

  a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
  }

  a:hover {
    color: var(--color-primary-dark);
  }

  button {
    font-family: inherit;
    cursor: pointer;
  }

  input, textarea, select {
    font-family: inherit;
  }

  * {
    box-sizing: border-box;
  }

  *::before, *::after {
    box-sizing: border-box;
  }
}

/* ===== COMPONENT STYLES ===== */
@layer components {

  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-md hover:shadow-lg;
    background: var(--color-primary);
    box-shadow: var(--shadow-primary);
  }

  .btn-primary:hover {
    background: var(--color-primary-dark);
    transform: translateY(-1px);
  }

  .btn-secondary {
    @apply bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500 shadow-md hover:shadow-lg;
    background: var(--color-secondary);
    box-shadow: var(--shadow-secondary);
  }

  .btn-outline {
    @apply border-2 bg-transparent hover:bg-primary-50 focus:ring-primary-500;
    border-color: var(--color-primary);
    color: var(--color-primary);
  }

  .btn-outline:hover {
    background-color: var(--color-neutral-50);
    border-color: var(--color-primary-dark);
    color: var(--color-primary-dark);
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-neutral-100 focus:ring-neutral-500;
    color: var(--color-text-secondary);
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  /* Card Components */
  .card {
    @apply rounded-xl border shadow-md transition-all duration-300;
    background: var(--color-bg-primary);
    border-color: var(--color-border-light);
    box-shadow: var(--shadow-md);
  }

  .card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }

  .card-elevated {
    @apply shadow-xl;
    box-shadow: var(--shadow-xl);
  }

  .card-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 102, 255, 0.1);
  }

  /* Input Components */
  .input {
    @apply w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
    background: var(--color-bg-primary);
    border-color: var(--color-border-medium);
    color: var(--color-text-primary);
  }

  .input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.1);
  }

  .input-error {
    @apply border-error-500 focus:ring-error-500;
    border-color: var(--color-error);
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
    background: rgba(0, 102, 255, 0.1);
    color: var(--color-primary-dark);
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
    background: rgba(16, 185, 129, 0.1);
    color: var(--color-success);
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
    background: rgba(245, 158, 11, 0.1);
    color: var(--color-warning);
  }

  .badge-error {
    @apply bg-error-100 text-error-800;
    background: rgba(220, 38, 38, 0.1);
    color: var(--color-error);
  }

  /* Navigation Components */
  .nav-link {
    @apply px-3 py-2 rounded-md text-sm font-medium transition-all duration-200;
    color: var(--color-text-secondary);
  }

  .nav-link:hover {
    color: var(--color-primary);
    background: var(--color-neutral-50);
  }

  .nav-link.active {
    color: var(--color-primary);
    background: rgba(0, 102, 255, 0.1);
  }

  /* Loading Components */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-neutral-200;
    border-top-color: var(--color-primary);
  }

  .loading-pulse {
    @apply animate-pulse bg-neutral-200 rounded;
  }

  /* Alert Components */
  .alert {
    @apply p-4 rounded-lg border-l-4;
  }

  .alert-info {
    @apply bg-primary-50 border-primary-500 text-primary-800;
    background: rgba(0, 102, 255, 0.05);
    border-left-color: var(--color-primary);
    color: var(--color-primary-dark);
  }

  .alert-success {
    @apply bg-success-50 border-success-500 text-success-800;
    background: rgba(16, 185, 129, 0.05);
    border-left-color: var(--color-success);
    color: var(--color-success);
  }

  .alert-warning {
    @apply bg-warning-50 border-warning-500 text-warning-800;
    background: rgba(245, 158, 11, 0.05);
    border-left-color: var(--color-warning);
    color: var(--color-warning);
  }

  .alert-error {
    @apply bg-error-50 border-error-500 text-error-800;
    background: rgba(220, 38, 38, 0.05);
    border-left-color: var(--color-error);
    color: var(--color-error);
  }
}

/* ===== UTILITY CLASSES ===== */
@layer utilities {

  /* Gradient Utilities */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-hero {
    background: var(--gradient-hero);
  }

  .gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Glass Morphism */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Neumorphism */
  .neuro {
    background: var(--color-neutral-100);
    box-shadow:
      20px 20px 60px #d1d5db,
      -20px -20px 60px #ffffff;
  }

  .neuro-inset {
    background: var(--color-neutral-100);
    box-shadow:
      inset 20px 20px 60px #d1d5db,
      inset -20px -20px 60px #ffffff;
  }

  /* Shadow Utilities */
  .shadow-primary {
    box-shadow: var(--shadow-primary);
  }

  .shadow-secondary {
    box-shadow: var(--shadow-secondary);
  }

  .shadow-colored {
    box-shadow: 0 10px 25px -5px rgba(0, 102, 255, 0.2);
  }

  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  /* Hover Effects */
  .hover-lift {
    transition: transform var(--transition-normal);
  }

  .hover-lift:hover {
    transform: translateY(-4px);
  }

  .hover-scale {
    transition: transform var(--transition-normal);
  }

  .hover-scale:hover {
    transform: scale(1.05);
  }

  .hover-glow {
    transition: box-shadow var(--transition-normal);
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 102, 255, 0.3);
  }

  /* Text Utilities */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Layout Utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-16 sm:py-20 lg:py-24;
  }

  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  /* Responsive Utilities */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Focus Utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500;
  }

  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500;
  }
}

/* ===== KEYFRAME ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== RESPONSIVE DESIGN HELPERS ===== */
@media (max-width: 640px) {
  .container-custom {
    @apply px-4;
  }

  .section-padding {
    @apply py-12;
  }

  h1 { font-size: var(--font-size-3xl); }
  h2 { font-size: var(--font-size-2xl); }
  h3 { font-size: var(--font-size-xl); }
}

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}
